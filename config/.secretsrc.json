{"files": [{"name": "private_key.pem", "description": "Pem file with the private key to sign token"}, {"name": "public_key.pub", "description": "File with the public key for validation"}], "secrets": [{"name": "ENVIROMENT", "type": "string", "description": "enviroment"}, {"name": "PRODUCT_NAME", "type": "string", "default": "shinrai-way-of-work", "description": "Seki product name"}, {"name": "PORT_GITHUB_API", "default": 8080, "description": "port github api", "type": "number"}, {"name": "API_KEY_GITHUB", "default": "", "description": "api github", "type": "string"}, {"name": "API_KEY_GITHUB_SVC", "default": "1122kjdnndpn", "description": "api KEY SERVICE", "type": "string"}, {"name": "URL_GITHUB", "default": "https://api.github.com", "description": "url github", "type": "string"}, {"name": "DATABASE_NAME", "default": "metrics", "description": "database name", "type": "string"}, {"name": "ULR_API_JIRA", "default": "https://cencosud.atlassian.net", "description": "url api jira", "type": "string"}, {"name": "KEY_API_JIRA", "default": "", "description": "key api jira", "type": "string"}, {"name": "USERNAME_JIRA", "default": "", "description": "username api jira", "type": "string"}, {"name": "URL_METRICS_API", "default": "", "description": "url api metrics", "type": "string"}, {"name": "PORT_METRICS_API", "default": "", "description": "port api metrics", "type": "string"}, {"name": "PORT_BFF_API", "default": "", "description": "port api bff", "type": "string"}, {"name": "PORT_PRODUCTS_API", "default": "", "description": "port api bff", "type": "string"}, {"name": "URL_PRODUCTS_API", "default": "", "description": "url products api", "type": "string"}, {"name": "PORT_BC_API", "description": "port for business-case-api", "type": "number"}, {"name": "PORT_CHAT_API", "description": "port for chat api", "type": "number"}, {"name": "URL_BUSINESS_CASE_API", "default": " ", "description": "url business-case api", "type": "string"}, {"name": "URL_FILE_MANAGER_API", "default": "", "description": "url file manager api", "type": "string"}, {"name": "URL_HERO_NEWS_API", "default": "", "description": "url hero news api", "type": "string"}, {"name": "URL_QUESTIONS_API", "default": "", "description": "url questions api", "type": "string"}, {"name": "URL_NEW_METRIC_API", "default": "", "description": "url metrics api", "type": "string"}, {"name": "URL_INITIATIVES_API", "default": "", "description": "url initiatives api", "type": "string"}, {"name": "PORT_FILE_MANAGER_API", "description": "port for file-manager-api", "type": "number"}, {"name": "IAM_API_KEY", "description": "api for iam", "type": "string"}, {"name": "IAM_SERVICE_URL", "description": "url service iam", "type": "string"}, {"name": "PORT_USERS_API", "description": "port for user api", "type": "number"}, {"name": "TOKEN_EXPIRATION_MINUTES", "description": "set token expiration", "type": "number"}, {"name": "URL_USER_API", "description": "url user api", "type": "string"}, {"name": "PORT_HERO_NEWS_API", "description": "port hero-news-api", "type": "number"}, {"name": "PORT_QUESTIONS_API", "description": "port questions-api", "type": "number"}, {"name": "PORT_INITIATIVES_API", "description": "port initiatives-api", "type": "number"}, {"name": "LOG_LEVEL", "description": "log level for logger winston", "type": "string"}, {"name": "PORT_INITIATIVE_SUBSCRIBER", "description": "port subscriber", "type": "number"}, {"name": "PORT_NEW_METRICS_API", "description": "port for metrics api", "type": "number"}, {"name": "TOPIC_FEATURE_VALORATION_UPDATE", "default": "topic", "type": "string"}, {"name": "PMO_API_KEY", "description": "api key for PMO auth", "type": "string"}, {"name": "PORT_TEAMWORK_API", "description": "port for team work api", "type": "number"}, {"name": "URL_TEAMWORK_API", "description": "url teamwork api", "type": "string"}, {"name": "URL_CAPABILITIES_API", "description": "url capabilities api", "type": "string"}, {"name": "AD_APIKEY", "description": "api key", "type": "string"}, {"name": "AD_URL_API", "description": "api url key", "type": "string"}, {"name": "ANTHROPIC_API_KEY", "description": "api key for anthropic", "type": "string"}]}