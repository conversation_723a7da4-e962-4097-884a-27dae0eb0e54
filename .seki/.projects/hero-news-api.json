{"name": "hero-news-api", "description": "hero news manager api", "settings": {"internal": true, "deployment": {"disableXyzDomain": true}, "stages": {"staging": {"replicas": 2, "minAvailableReplicas": "80%"}, "production": {"replicas": 2, "minAvailableReplicas": "80%"}}}, "$schema": "https://api.cencosudx.xyz/seki/statics/proxy/Cencosud-xlabs/seki-project-templates/main/api/schema.json", "workspace_path": "/Users/<USER>/Desktop/Proyects-Cenco/shinrai-way-of-work", "group_folder": "apps", "path": "hero-news-api", "kind": "api", "definition": {"name": "Api", "icons": {"light": "icons/icon.svg", "dark": "icons/icon.svg"}, "url": "https://github.com/Cencosud-xlabs/seki-project-templates.git"}, "alias": "hero-news-api", "skip": true}