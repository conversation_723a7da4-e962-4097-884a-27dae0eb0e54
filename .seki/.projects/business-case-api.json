{"name": "business-case-api", "description": "business-case-api", "settings": {"deployment": {"disableXyzDomain": true}, "stages": {"staging": {"replicas": 2}, "production": {"replicas": 3, "resources": {"cpu": 600, "memory": 1024}}}}, "skip": true, "$schema": "https://api.cencosudx.xyz/seki/statics/proxy/Cencosud-xlabs/seki-project-templates/main/api/schema.json", "workspace_path": "/Users/<USER>/Documents/shinrai-way-of-work", "group_folder": "apps", "path": "business-case-api", "kind": "api", "definition": {"name": "Api", "icons": {"light": "icons/icon.svg", "dark": "icons/icon.svg"}, "url": "https://github.com/Cencosud-xlabs/seki-project-templates.git"}, "alias": "business-case-api"}