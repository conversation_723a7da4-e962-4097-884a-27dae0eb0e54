{"name": "metrics", "description": "microservice to return contribution inform", "skip": true, "settings": {"internal": true, "deployment": {"disableXyzDomain": true}, "stages": {"staging": {"replicas": 2}, "production": {"replicas": 3, "minAvailableReplicas": "80%"}}}, "$schema": "https://api.cencosudx.xyz/seki/statics/proxy/Cencosud-xlabs/seki-project-templates/main/api/schema.json", "workspace_path": "/Users/<USER>/Documents/cencoEnterprise/shinrai-way-of-work", "group_folder": "apps", "path": "metrics", "kind": "api", "definition": {"name": "Api", "icons": {"light": "icons/icon.svg", "dark": "icons/icon.svg"}, "url": "https://github.com/Cencosud-xlabs/seki-project-templates.git"}, "alias": "metrics"}