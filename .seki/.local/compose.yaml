version: '3.8'
services:
  mongodb:
    image: mongo:latest
    ports:
      - '27017:27017'
  bucket:
    image: oittaa/gcp-storage-emulator
    command: start --default-bucket=shinrai-wow--bucket
    environment:
      PORT: 9023
    ports:
      - '9023:9023'
  pubsub:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:455.0.0-emulators
    entrypoint:
      - gcloud
      - beta
      - emulators
      - pubsub
      - start
      - '--project=cencosudx'
      - '--host-port=0.0.0.0:8085'
    ports:
      - '8085:8085'
  postgres:
    image: postgres:14.0
    command: |
      /bin/bash -c "
        echo '
            CREATE DATABASE SO;
          ' > /docker-entrypoint-initdb.d/init.sql &&
        docker-entrypoint.sh postgres
      "
    ports:
      - '5432:5432'
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USERNAME: postgres
      POSTGRES_PASSWORD: postgres
  pgadmin:
    image: dpage/pgadmin4:7.8
    user: root
    depends_on:
      - postgres
    ports:
      - '9001:80'
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: root
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    entrypoint: >
      /bin/sh -c ' echo "postgres:5432:*:postgres:postgres" > /tmp/pgpass; chmod
      600 /tmp/pgpass; echo
      "{\"Servers\":{\"1\":{\"Name\":\"local\",\"Group\":\"Servers\",\"Host\":\"postgres\",\"Port\":5432,\"MaintenanceDB\":\"postgres\",\"Username\":\"postgres\",\"PassFile\":\"/tmp/pgpass\",\"SSLMode\":\"prefer\"}}}"
      > /pgadmin4/servers.json; /entrypoint.sh '
