import * as SDK from '@team_yumi/node-sdk';
import * as XLSX from 'xlsx';
import { Response } from 'express';
import { HttpStatus, Inject, Injectable, OnModuleInit } from '@nestjs/common';
import {
  AreaTeamWorkRepository,
  ExchangeRateRepository,
  LicenseRepository,
  PartnerRepository,
  PersonRepository,
  RoleTeamWorkRepository,
  SkillRepository,
  SubareaTeamWorkRepository,
  SubManagementTeamWorkRepository,
} from '@shinrai-way-of-work/core';
import {
  CustomJwtEntity,
  IPerson,
  IShared,
  Person,
  PersonQueryDto,
} from '@shinrai-way-of-work/models';
import {
  ApiResponse,
  doTryCatch,
  filterObjectToQuery,
  searchText,
} from '@shinrai-way-of-work/utils';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { PipelineStage, Types } from 'mongoose';
import { <PERSON>WithDollar, PersonWithRelationsGetResponse } from './type';
import { HistoryLogService, PermissionService } from '@shinrai-way-of-work/shared';
import secrets from '../../config/secrets';
import axios from 'axios';
@Injectable()
export class PersonService implements OnModuleInit {
  constructor(
    private readonly personRepository: PersonRepository,
    private readonly roleRepository: RoleTeamWorkRepository,
    private readonly partnerRepository: PartnerRepository,
    private readonly licenseRepository: LicenseRepository,
    private readonly permissionService: PermissionService,
    private readonly exchangeRateRepository: ExchangeRateRepository,
    private readonly areaTeamWorkRepository: AreaTeamWorkRepository,
    private readonly subareaRepository: SubareaTeamWorkRepository,
    private readonly subManagementRepository: SubManagementTeamWorkRepository,
    private readonly skillRepository: SkillRepository,
    private readonly historyLogService: HistoryLogService<Person>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  onModuleInit() {
    this.historyLogService.watch(this.personRepository.personModel, Person.name);
  }

  async findAll(query: PersonQueryDto, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { limit, offset, search, ...restQuery } = query;
      const queryObject = filterObjectToQuery({ ...restQuery, is_active: true }, {});
      const result = await this.personRepository.findPageable({
        aggregation: [
          {
            $match: queryObject,
          },
          { $sort: { id: 1 } },
          ...this.getAggregate(),
          this.getFilterSearch(search),
        ],
        paginationOptions: {
          offset: offset || 0,
          size: limit || 10,
        },
      });

      const personWithCostInDollar = await this.calculateCostInUSD(result.data);
      //if the user has permission to see the cost in dollar, we apply the cost in dollar to each person
      const dataWithPermissions = await this.applyPermissionsToData(
        personWithCostInDollar,
        user,
        ['localCost', 'costInDollar', 'monthlyLocalCost', 'monthlyCostInDollar'],
        'squad',
        'admin'
      );

      return ApiResponse.TruePaginate({
        data: dataWithPermissions,
        metadata: result.metadata,
      });
    });
  }

  async search(query: PersonQueryDto, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { limit, offset, search, ...restQuery } = query;
      const queryObject = filterObjectToQuery({ ...restQuery, is_active: true }, {});
      const result = await this.personRepository.findPageable({
        aggregation: [
          {
            $match: queryObject,
          },
          ...this.getAggregate(),
          this.getFilterSearch(search),
        ],
        paginationOptions: {
          offset: offset || 0,
          size: limit || 10,
        },
      });

      const personWithCostInDollar = await this.calculateCostInUSD(result.data);
      //if the user has permission to see the cost in dollar, we apply the cost in dollar to each person
      const dataWithPermissions = await this.applyPermissionsToData(
        personWithCostInDollar,
        user,
        ['localCost', 'costInDollar', 'monthlyLocalCost', 'monthlyCostInDollar'],
        'squad',
        'admin'
      );

      return ApiResponse.TruePaginate({
        data: dataWithPermissions,
        metadata: result.metadata,
      });
    });
  }

  async create(data: IPerson.Create, user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      this.logger.debug(`Creating new person ${data}`);

      /* const roleValidation = await this.validateRoleExistence(data.roleId);
      if (!roleValidation.success) {
        return Promise.reject(roleValidation);
      }

      if (data.partnerId) {
        const partnerValidation = await this.validatePartnerExistence(data.partnerId);
        if (!partnerValidation.success) {
          return Promise.reject(partnerValidation);
        }
      }

      if (data.areaId) {
        const areaValidation = await this.validateAreaExistence(data.areaId);
        if (!areaValidation.success) return Promise.reject(areaValidation);
      }

      if (data.subareaId && data.areaId) {
        const subareaValidation = await this.validateSubareaBelongsToArea(data.subareaId);
        if (!subareaValidation.success) return Promise.reject(subareaValidation);
      }

      const licensesValidation = await this.validateLicenseExistence(data.licenses);
      if (!licensesValidation.success) {
        return Promise.reject(licensesValidation);
      } */

      const getLastId = async (): Promise<number> => {
        const lastPerson = await this.personRepository.personModel.findOne().sort({ id: -1 });
        return lastPerson?.id || 0;
      };

      if (data.entryDate && typeof data.period === 'number') {
        const entryDate = new Date(data.entryDate);
        const exitDate = new Date(
          entryDate.setMonth(entryDate.getMonth() + data.period)
        ).toISOString();
        data.exitDate = exitDate;
      }

      const result = await this.personRepository.create({
        id: (await getLastId()) + 1,
        ...data,
        fullName: data.fullName?.length ? data.fullName : `${data.name} ${data.lastName}`,
        created_by: {
          email: user.email,
          name: user.unique_name,
          identifier: user.primarysid,
        },
      });
      return ApiResponse.True({
        data: result,
        statusCode: HttpStatus.CREATED,
      });
    });
  }

  async bulkData(data: IPerson.Create[], user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const results: {
        index: number;
        success: boolean;
        message?: string;
        data?: Person;
      }[] = [];

      const getLastId = async (): Promise<number> => {
        const lastPerson = await this.personRepository.personModel.findOne().sort({ id: -1 });
        return lastPerson?.id || 0;
      };

      let currentId = await getLastId();

      for (let i = 0; i < data.length; i++) {
        const person = data[i];
        try {
          const existingPerson = await this.personRepository.personModel.findOne({
            $or: [{ email: person.email }, { codeAd: person.codeAd }],
            is_active: true,
          });

          if (existingPerson) {
            const updatedPerson = await this.personRepository.update({
              query: { _id: existingPerson._id },
              data: {
                $set: {
                  ...person,
                  updated_by: {
                    email: user.email,
                    name: user.unique_name,
                    identifier: user.primarysid,
                  },
                },
              },
            });

            results.push({
              index: i,
              success: true,
              data: updatedPerson,
            });
            continue;
          }

          const result = await this.personRepository.create({
            id: ++currentId,
            ...person,
            created_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          });

          results.push({
            index: i,
            success: true,
            data: result,
          });
        } catch (error) {
          this.logger.error(`Error in bulkData at index ${i}: ${error?.message || error}`);
          results.push({
            index: i,
            success: false,
            message: error?.message || 'Unknown error',
          });
        }
      }

      const total = results.length;
      const successCount = results.filter((r) => r.success).length;
      const failCount = total - successCount;

      if (successCount === total) {
        return ApiResponse.True({
          data: results,
          statusCode: HttpStatus.OK,
          message: `Se cargaron exitosamente ${successCount} registros.`,
        });
      } else if (successCount > 0) {
        return ApiResponse.False({
          data: results,
          statusCode: 500,
          message: `Se procesaron ${successCount} registros exitosamente y ${failCount} fallaron.`,
          errorCode: 'PARTIAL_SUCCESS',
        });
      } else {
        return ApiResponse.False({
          data: results,
          statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
          message: 'No se pudo cargar ningún registro.',
          errorCode: 'BULK_FAILURE',
        });
      }
    });
  }

  async update(dto: IShared.RequestIdDto, data: IPerson.Update, user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;
      this.logger.debug(`Updating person ${id} with data ${JSON.stringify(data)}`);
      const exist = await this.personRepository.findOne({ query: { _id: id } });
      if (!exist) {
        return ApiResponse.False({
          message: 'Person not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const getLastId = async (): Promise<number> => {
        const lastPerson = await this.personRepository.personModel.findOne().sort({ id: -1 });
        return lastPerson?.id || 0;
      };

      const getPersonId = async (id: string): Promise<number> => {
        const person = await this.personRepository.findOne({ query: { _id: id } });
        return person?.id;
      };

      const currentId = await getPersonId(id);
      const newId = currentId || (await getLastId()) + 1;

      if (data.entryDate && typeof data.period === 'number') {
        const entryDate = new Date(data.entryDate);
        const exitDate = new Date(
          entryDate.setMonth(entryDate.getMonth() + data.period)
        ).toISOString();
        data.exitDate = exitDate;
      }

      const result = await this.personRepository.update({
        query: { _id: id },
        data: {
          $set: {
            id: newId,
            fullName: data.fullName?.length ? data.fullName : `${data.name} ${data.lastName}`,
            ...data,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.OK });
    });
  }

  async delete(dto: IShared.RequestIdDto, user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;
      this.logger.debug(`Deleting person ${id}`);
      const exist = await this.personRepository.findOne({ query: { _id: id } });
      if (!exist) {
        return ApiResponse.False({
          message: 'Person not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      await this.personRepository.update({
        query: { _id: id },
        data: {
          $set: {
            is_active: false,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        },
      });

      return ApiResponse.True({
        message: 'Person deleted successfully',
        statusCode: HttpStatus.OK,
      });
    });
  }

  async export(query: PersonQueryDto, res: Response, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { limit, offset, ...restQuery } = query;
      const result = (await this.personRepository.find({
        aggregation: [
          {
            $match: filterObjectToQuery({ ...restQuery, is_active: true }, {}),
          },
          ...this.getAggregate(),
        ],
      })) as unknown as PersonWithRelationsGetResponse[];

      //Add columns for compare
      const enrichedPeople = await Promise.all(
        result.map(async (person) => {
          try {
            const adUsers: any[] = await this.findAllADUsers(person.email);

            const match = adUsers.find((user) => {
              const proxyAddresses = user.proxyAddresses?.map((addr) =>
                addr.toLowerCase().replace(/^smtp:/i, '')
              );

              return [user.userPrincipalName, user.mail, ...(proxyAddresses || [])]
                .filter(Boolean)
                .map((val) => val.toLowerCase())
                .includes(person.email.toLowerCase());
            });

            if (match) {
              return {
                ...person,
                ApiTitle: match.title,
                ApiDepartment: match.department,
                ApiManager: match.manager,
                ApiPartner: match.company,
              };
            }
            return person;
          } catch (error) {
            return person;
          }
        })
      );

      //calculate cost in dollar to each person
      const personWithCostInDollar = await this.calculateCostInUSD(enrichedPeople);

      //if the user has permission to see the cost in dollar, we apply the cost in dollar to each person
      const dataWithPermissions = await this.applyPermissionsToData(
        personWithCostInDollar,
        user,
        ['localCost', 'costInDollar'],
        'squad',
        'admin'
      );

      const data = (
        dataWithPermissions as (PersonWithRelationsGetResponse & {
          area?: { name: string };
          subarea?: { name: string };
        })[]
      ).map((person) => {
        return {
          ...person,
          _id: String(person?._id) || '',
          role: person?.role?.name || '',
          area: person?.area?.name || '',
          subarea: person?.subarea?.name || '',
          partner: person?.partner?.name || '',
          created_by: person?.created_by?.name || '',
          licenses: person?.licenses?.map((l) => l.name).join(', ') || '',
          skills: person?.skills?.map((s) => s.name).join(', ') || '',
        };
      });

      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'people');

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      return res.send(buffer);
    });
  }

  async exportHistory(query: IShared.HistoryQueryDto, res: Response) {
    return doTryCatch(this.logger, async () => {
      const history = await this.historyLogService.getHistory(Person.name, query);
      const data = history.data.map((item) => ({
        updated_at: item.updated_at,
        updated_by: item.updated_by.email,
        fields_changed: JSON.stringify(Object.keys(item.changes)),
        changes: Object.entries(item.changes)
          .filter(([key]) => key !== 'updated_by')
          .map(([field, newValue]) => this.formatChange(field, newValue))
          .join(''),
      }));
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'history_people');

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      return res.send(buffer);
    });
  }

  async getAllSelectItems() {
    return doTryCatch(this.logger, async () => {
      const [roles, partners, licenses, areas, subareas, countriesRaw, skills, submanagements] =
        await Promise.all([
          this.roleRepository.find({ query: { is_active: true } }),
          this.partnerRepository.find({ query: { is_active: true } }),
          this.licenseRepository.find({ query: { is_active: true } }),
          this.areaTeamWorkRepository.find({ query: { is_active: true } }),
          this.subareaRepository.find({ query: { is_active: true } }),
          this.exchangeRateRepository.find({ query: { is_active: true } }),
          this.skillRepository.find({ query: { is_active: true } }),
          this.subManagementRepository.find({ query: { is_active: true } }),
        ]);

      const countries = countriesRaw.map((value) => ({
        value: value.country,
        label: value.countryName,
        currency: value.currency,
        currencyLabel: value.currencyName,
      }));

      const type = ['External', 'Internal'].map((type) => ({
        value: type.toLocaleLowerCase(),
        label: type,
      }));

      const seniority = ['ssr', 'sr', 'jr'].map((seniority) => ({
        value: seniority.toLocaleLowerCase(),
        label: seniority,
      }));

      const result = {
        roles: roles.map((role) => ({ value: role._id, label: role.name })),
        partners: partners.map((partner) => ({ value: partner._id, label: partner.name })),
        licenses: licenses.map((license) => ({ value: license._id, label: license.name })),
        countries,
        seniority,
        type,
        areas: areas.map((area) => ({ value: area._id, label: area.name })),
        subareas: subareas.map((subarea) => ({
          value: subarea._id,
          label: subarea.name,
          area_id: subarea.area_id,
        })),
        submanagements: submanagements.map((subManagement) => ({
          value: subManagement._id,
          label: subManagement.name,
          subarea_id: subManagement.subarea_id,
        })),
        skills: skills.map((skill) => ({ value: skill._id, label: skill.name })),
      };

      return ApiResponse.True({
        data: result,
        statusCode: HttpStatus.OK,
      });
    });
  }

  async getHistory(query: IShared.HistoryQueryDto) {
    return doTryCatch(this.logger, async () => {
      const result = await this.historyLogService.getHistory(Person.name, query);
      return ApiResponse.TruePaginate({ data: result.data, metadata: result.metadata });
    });
  }

  private getAggregate() {
    return [
      {
        $lookup: {
          from: 'roleteamwork',
          localField: 'roleId',
          foreignField: '_id',
          as: 'role',
          pipeline: [
            {
              $project: {
                name: 1,
                shortName: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'license',
          localField: 'licenses',
          foreignField: '_id',
          as: 'licenses',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'partner',
          localField: 'partnerId',
          foreignField: '_id',
          as: 'partner',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'skill',
          localField: 'skills',
          foreignField: '_id',
          as: 'skills',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'areateamwork',
          localField: 'areaId',
          foreignField: '_id',
          as: 'area',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'subareateamwork',
          localField: 'subareaId',
          foreignField: '_id',
          as: 'subarea',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'submanagement',
          localField: 'submanagementId',
          foreignField: '_id',
          as: 'sub_management',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$area',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$subarea',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$sub_management',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$role',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$partner',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          roleId: 0,
        },
      },
    ];
  }

  private async validateRoleExistence(roleId: string): Promise<ApiResponse> {
    const role = await this.roleRepository.findOne({ query: { _id: roleId } });
    if (!role) {
      return ApiResponse.False({
        message: 'Role not found',
        statusCode: HttpStatus.NOT_FOUND,
        success: false,
      });
    }
    return ApiResponse.True({
      success: true,
    });
  }

  private async validatePartnerExistence(partnerId: string): Promise<ApiResponse> {
    const partner = await this.partnerRepository.findOne({ query: { _id: partnerId } });
    if (!partner) {
      return ApiResponse.False({
        message: 'Partner not found',
        statusCode: HttpStatus.NOT_FOUND,
        success: false,
      });
    }
    return ApiResponse.True({
      success: true,
    });
  }

  private async validateLicenseExistence(licenses: Types.ObjectId[]): Promise<ApiResponse> {
    const licensesExist = licenses.map((licenseId) => {
      const exist = this.licenseRepository.findOne({ query: { _id: licenseId } });
      if (!exist) {
        return ApiResponse.False({
          message: 'License not found',
          statusCode: HttpStatus.NOT_FOUND,
          success: false,
        });
      }
      return ApiResponse.True({
        success: true,
      });
    });

    if (licensesExist.some((license) => !license.success)) {
      return ApiResponse.False({
        message: 'License not found',
        statusCode: HttpStatus.NOT_FOUND,
        success: false,
      });
    }
    return ApiResponse.True({
      success: true,
    });
  }

  private async validateAreaExistence(areaId: string): Promise<ApiResponse> {
    const area = await this.areaTeamWorkRepository.findOne({ query: { _id: areaId } });
    if (!area) {
      return ApiResponse.False({
        message: 'Area not found',
        statusCode: HttpStatus.NOT_FOUND,
        success: false,
      });
    }
    return ApiResponse.True({ success: true });
  }

  private async validateSubareaBelongsToArea(subareaId: string): Promise<ApiResponse> {
    const subarea = await this.subareaRepository.findOne({ query: { _id: subareaId } });
    if (!subarea) {
      return ApiResponse.False({
        message: 'Subarea not found',
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
      });
    }
    return ApiResponse.True({ success: true });
  }

  private async applyPermissionsToData<T>(
    data: T[],
    user: CustomJwtEntity,
    restrictedFields: (keyof T)[],
    permissionResource: string,
    permissionAction: string
  ): Promise<T[]> {
    const hasPermission = await this.permissionService.hasPermission(
      permissionResource,
      permissionAction,
      user.scopes
    );

    if (hasPermission) {
      return data;
    }

    return data.map((item) => {
      const filteredItem = { ...item };
      restrictedFields.forEach((field) => {
        delete filteredItem[field];
      });
      return filteredItem;
    });
  }

  private getFilterSearch(search?: string): PipelineStage {
    return {
      $match: {
        ...(search && {
          ...searchText(search, [
            'fullName',
            'role.name',
            'role.shortName',
            'area.name',
            'subarea.name',
            'sub_management.name',
            'partner.name',
            'licenses.name',
            'skills.name',
          ]),
        }),
      },
    };
  }

  private formatChange(field: string, newValue: any): string {
    if (Array.isArray(newValue)) {
      return `${field} fue actualizado a: [${newValue.join(', ')}]`;
    } else if (typeof newValue === 'object' && newValue !== null) {
      return Object.entries(newValue)
        .map(([key, value]) => `${field} (${key}) fue actualizado a: '${value}'`)
        .join(', ');
    } else {
      return `${field} fue actualizado a: '${newValue}'`;
    }
  }

  async calculateCostInUSD<T extends PersonWithDollar>(data: T[]): Promise<T[]> {
    const exchangeRates = await this.exchangeRateRepository.find({
      query: { is_active: true },
    });

    const exchangeRateMap = new Map(
      exchangeRates.map((item) => [item.currency.toLowerCase(), item.rateToUsd])
    );

    return data.map((person) => {
      const currency = person.currency?.toLowerCase();
      const rate = exchangeRateMap.get(currency);
      const hours = person.hour || 0;
      const localCost = person.localCost || 0;

      const monthlyLocalCost = +(localCost * hours).toFixed(2);

      if (!rate || rate === 0) {
        this.logger.warn(`[COST] Tipo de cambio no encontrado o inválido para: ${person.currency}`);
        return {
          ...person,
          costInDollar: 0,
          monthlyLocalCost,
          monthlyCostInDollar: 0,
        };
      }

      const costInDollar = +(localCost / rate).toFixed(2);
      const monthlyCostInDollar = +(monthlyLocalCost / rate).toFixed(2);

      return {
        ...person,
        costInDollar,
        monthlyLocalCost,
        monthlyCostInDollar,
      };
    });
  }

  async findAllADUsers(search: string): Promise<[{ [key: string]: string }]> {
    const response = await axios.get(`${secrets.AD_URL_API}/find_all/${search}`, {
      headers: {
        apiKey: secrets.AD_APIKEY,
      },
    });
    return response.data.users;
  }
}
