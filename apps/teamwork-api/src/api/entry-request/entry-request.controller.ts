import * as SDK from '@team_yumi/node-sdk';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { QueryValidationInterceptor } from '@shinrai-way-of-work/utils';
import { EntryRequestService } from './entry-request.service';
import {
  CustomJwtEntity,
  EntryRequestCreateDto,
  EntryRequestQueryDto,
  EntryRequestUpdateDto,
  IEntryRequest,
  IShared,
  StatusCommentDto,
} from '@shinrai-way-of-work/models';
import { createZodDto } from 'nestjs-zod';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@SDK.Lib.Nest.Modules.Jwt.Authenticated()
@ApiBearerAuth('defaultBearerAuth')
@ApiTags('entry-request')
@Controller('entry-request')
export class EntryRequestController {
  constructor(private readonly entryRequestService: EntryRequestService) {}

  @Get('')
  @ApiOperation({ summary: 'Get all entry requests' })
  @ApiResponse({ status: 200, description: 'Get all entry requests' })
  @UseInterceptors(new QueryValidationInterceptor(EntryRequestQueryDto))
  async find(@Query() query: EntryRequestQueryDto, @Res() res: Response<unknown>) {
    const result = await this.entryRequestService.findAll(query);
    return res.status(result.statusCode).json(result);
  }

  @Post('')
  @ApiOperation({ summary: 'Create a new entry request' })
  @ApiResponse({ status: 201, description: 'Entry request created' })
  @ApiBody({ type: createZodDto(EntryRequestCreateDto), required: true })
  async create(
    @Body(new ZodValidationPipe(EntryRequestCreateDto)) data: IEntryRequest.Create,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.entryRequestService.create(data, user);
    return res.status(result.statusCode).json(result);
  }

  @Patch('/:id')
  @ApiOperation({ summary: 'Update entry request by id' })
  @ApiResponse({ status: 200, description: 'Update entry request by id' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'entry request id',
    type: 'string',
  })
  @ApiBody({ type: createZodDto(EntryRequestUpdateDto), required: true })
  async update(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(EntryRequestUpdateDto)) data: IEntryRequest.Update,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.entryRequestService.update(id, data, user);
    return res.status(result.statusCode).json(result);
  }

  @Patch(':id/forward')
  @ApiOperation({ summary: 'Forward request for correction' })
  @ApiBody({ type: createZodDto(StatusCommentDto) })
  async forward(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(StatusCommentDto)) dto: { subject: string; comment: string },
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response
  ) {
    const result = await this.entryRequestService.forwardForCorrection(id.id, user, dto);
    return res.status(result.statusCode).json(result);
  }

  @Patch(':id/resend')
  @ApiOperation({ summary: 'Resend request for approval' })
  @ApiBody({ type: createZodDto(StatusCommentDto) })
  async resend(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(StatusCommentDto)) dto: { subject: string; comment: string },
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response
  ) {
    const result = await this.entryRequestService.resendForApproval(id.id, user, dto);
    return res.status(result.statusCode).json(result);
  }

  @Patch(':id/accept')
  @ApiOperation({ summary: 'Accept request' })
  @ApiBody({ type: createZodDto(StatusCommentDto) })
  async accept(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(StatusCommentDto)) dto: { subject: string; comment: string },
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response
  ) {
    const result = await this.entryRequestService.accept(id.id, user, dto);
    return res.status(result.statusCode).json(result);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'Approve request' })
  @ApiBody({ type: createZodDto(StatusCommentDto) })
  async approve(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(StatusCommentDto)) dto: { subject: string; comment: string },
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response
  ) {
    const result = await this.entryRequestService.approve(id.id, user, dto);
    return res.status(result.statusCode).json(result);
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Reject request' })
  @ApiBody({ type: createZodDto(StatusCommentDto) })
  async reject(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(StatusCommentDto)) dto: { subject: string; comment: string },
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response
  ) {
    const result = await this.entryRequestService.reject(id.id, user, dto);
    return res.status(result.statusCode).json(result);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update entry request by id' })
  @ApiResponse({ status: 200, description: 'Update entry request by id' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'entry request id',
    type: 'string',
  })
  @ApiBody({ type: createZodDto(EntryRequestUpdateDto), required: true })
  async updateById(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(EntryRequestUpdateDto)) data: IEntryRequest.Update,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.entryRequestService.update(id, data, user);
    return res.status(result.statusCode).json(result);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a entry request' })
  @ApiResponse({ status: 200, description: 'Return the deleted entry request ' })
  async delete(
    @Param() id: IShared.RequestIdDto,
    @Res() res: Response<unknown>,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity
  ) {
    const result = await this.entryRequestService.delete(id, user);
    return res.status(result.statusCode).json(result);
  }
}
