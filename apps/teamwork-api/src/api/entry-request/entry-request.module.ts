import { Module } from '@nestjs/common';
import { EntryRequestController } from './entry-request.controller';
import { EntryRequestService } from './entry-request.service';
import { EntryRequestRepositoryModule } from '@shinrai-way-of-work/core';

@Module({
  controllers: [EntryRequestController],
  providers: [EntryRequestService],
  imports: [EntryRequestRepositoryModule.register()],
})
export class EntryRequestModule {}
