import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { EntryRequestRepository } from '@shinrai-way-of-work/core';
import {
  CustomJwtEntity,
  EntryRequestQueryDto,
  IEntryRequest,
  IShared,
  StatusCommentDto,
} from '@shinrai-way-of-work/models';
import {
  ApiResponse,
  doTryCatch,
  filterObjectToQuery,
  searchText,
  TeamWorkEnum,
} from '@shinrai-way-of-work/utils';
import { Types } from 'mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Injectable()
export class EntryRequestService {
  constructor(
    private readonly entryRequestRepository: EntryRequestRepository,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  async findAll(query: EntryRequestQueryDto) {
    return doTryCatch(this.logger, async () => {
      const { offset, limit, search, ...restQuery } = query;
      const queryObject = filterObjectToQuery({ ...restQuery }, {});
      const result = await this.entryRequestRepository.findPageable({
        aggregation: [
          {
            $match: queryObject,
          },
          ...this.getAggregate(),
          this.getFilterSearch(search),
        ],
        paginationOptions: {
          offset: offset || 0,
          size: limit || 10,
        },
      });

      return ApiResponse.TruePaginate(result);
    });
  }

  async create(data: IEntryRequest.Create, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      this.logger.debug(`Creating new entry request ${JSON.stringify(data)}`);

      const getLastId = async (): Promise<number> => {
        const lastRequest = await this.entryRequestRepository.entryRequestModel
          .findOne()
          .sort({ id: -1 });
        return lastRequest?.id || 0;
      };

      const calculateExpectedDateEnd = (months: number, startDate: Date): Date => {
        const endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + months);
        return endDate;
      };

      const result = await this.entryRequestRepository.create({
        id: (await getLastId()) + 1,
        ...data,
        area: new Types.ObjectId(data.area),
        subarea: new Types.ObjectId(data.subarea),
        submanagement: new Types.ObjectId(data.submanagement),
        role: new Types.ObjectId(data.role),
        budget_total: data.budget * data.months_long,
        status_history: [
          {
            status: TeamWorkEnum.EntryRequestStatus.Pending,
            previous_status: TeamWorkEnum.EntryRequestStatus.Pending,
            subject: data.topic,
            comment: data.comment,
            user: user.unique_name,
            email: user.email,
            date: new Date(),
          },
        ],
        expected_date_end: calculateExpectedDateEnd(data.months_long, data.expected_date_start),
        request_date: new Date(),
        created_by: {
          email: user.email,
          name: user.unique_name,
          identifier: user.primarysid,
        },
      });
      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async forwardForCorrection(id: string, user: CustomJwtEntity, dto: StatusCommentDto) {
    return doTryCatch(this.logger, async () => {
      const entry = await this.entryRequestRepository.findById({ id });
      if (!entry) {
        return ApiResponse.False({
          message: 'Entry request not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const result = await this.entryRequestRepository.update({
        query: { _id: id },
        data: {
          $set: {
            status: TeamWorkEnum.EntryRequestStatus.Forwarded,
          },
          $push: {
            status_history: {
              previous_status: entry.status,
              status: TeamWorkEnum.EntryRequestStatus.Forwarded,
              user: user.unique_name,
              email: user.email,
              date: new Date(),
              comment: dto.comment,
              subject: dto.subject,
            },
          },
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async resendForApproval(id: string, user: CustomJwtEntity, dto: StatusCommentDto) {
    return doTryCatch(this.logger, async () => {
      const entry = await this.entryRequestRepository.findById({ id });
      if (!entry) {
        return ApiResponse.False({
          message: 'Entry request not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const result = await this.entryRequestRepository.update({
        query: { _id: id },
        data: {
          $set: {
            status: TeamWorkEnum.EntryRequestStatus.Pending,
          },
          $push: {
            status_history: {
              previous_status: entry.status,
              status: TeamWorkEnum.EntryRequestStatus.Pending,
              user: user.unique_name,
              email: user.email,
              date: new Date(),
              comment: dto.comment,
              subject: dto.subject,
            },
          },
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async accept(id: string, user: CustomJwtEntity, dto: StatusCommentDto) {
    return doTryCatch(this.logger, async () => {
      const entry = await this.entryRequestRepository.findById({ id });
      if (!entry) {
        return ApiResponse.False({
          message: 'Entry request not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const result = await this.entryRequestRepository.update({
        query: { _id: id },
        data: {
          $set: {
            status: TeamWorkEnum.EntryRequestStatus.Accepted,
          },
          $push: {
            status_history: {
              previous_status: entry.status,
              status: TeamWorkEnum.EntryRequestStatus.Accepted,
              user: user.unique_name,
              email: user.email,
              date: new Date(),
              comment: dto.comment,
              subject: dto.subject,
            },
          },
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async approve(id: string, user: CustomJwtEntity, dto: StatusCommentDto) {
    return doTryCatch(this.logger, async () => {
      const entry = await this.entryRequestRepository.findById({ id });
      if (!entry) {
        return ApiResponse.False({
          message: 'Entry request not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const result = await this.entryRequestRepository.update({
        query: { _id: id },
        data: {
          $set: {
            status: TeamWorkEnum.EntryRequestStatus.Approved,
          },
          $push: {
            status_history: {
              previous_status: entry.status,
              status: TeamWorkEnum.EntryRequestStatus.Approved,
              user: user.unique_name,
              email: user.email,
              date: new Date(),
              comment: dto.comment,
              subject: dto.subject,
            },
          },
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async reject(id: string, user: CustomJwtEntity, dto: StatusCommentDto) {
    return doTryCatch(this.logger, async () => {
      const entry = await this.entryRequestRepository.findById({ id });
      if (!entry) {
        return ApiResponse.False({
          message: 'Entry request not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      const result = await this.entryRequestRepository.update({
        query: { _id: id },
        data: {
          $set: {
            status: TeamWorkEnum.EntryRequestStatus.Rejected,
          },
          $push: {
            status_history: {
              previous_status: entry.status,
              status: TeamWorkEnum.EntryRequestStatus.Rejected,
              subject: dto.subject,
              comment: dto.comment,
              user: user.unique_name,
              email: user.email,
              date: new Date(),
            },
          },
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async update(dto: IShared.RequestIdDto, data: IEntryRequest.Update, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      this.logger.debug(`Updating entry request ${JSON.stringify(data)}`);
      const { id } = dto;

      const result = await this.entryRequestRepository.update({
        query: { _id: id },
        data: {
          $set: {
            ...data,
            area: new Types.ObjectId(data.area),
            subarea: new Types.ObjectId(data.subarea),
            submanagement: new Types.ObjectId(data.submanagement),
            role: new Types.ObjectId(data.role),
            budget_total: data.budget * data.months_long,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        },
      });
      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async delete(dto: IShared.RequestIdDto, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;
      const exist = await this.entryRequestRepository.findOne({
        query: { _id: id },
      });
      if (!exist) {
        return ApiResponse.False({
          message: 'Entry request not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      await this.entryRequestRepository.delete(id);

      return ApiResponse.True({ statusCode: HttpStatus.OK });
    });
  }

  private getFilterSearch(search: string) {
    return {
      $match: {
        ...(search && {
          ...searchText(search, [
            'manager',
            'role',
            'budget',
            'budget_type',
            'budget_line',
            'months_long',
            'expected_date_start',
            'expected_date_end',
            'topic',
            'comment',
            'area.name',
            'subarea.name',
          ]),
        }),
      },
    };
  }

  private getAggregate() {
    return [
      {
        $lookup: {
          from: 'areateamwork',
          localField: 'area',
          foreignField: '_id',
          as: 'area',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'subareateamwork',
          localField: 'subarea',
          foreignField: '_id',
          as: 'subarea',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'submanagement',
          localField: 'submanagement',
          foreignField: '_id',
          as: 'submanagement',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'roleteamwork',
          localField: 'role',
          foreignField: '_id',
          as: 'role',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: '$area',
      },
      {
        $unwind: '$subarea',
      },
      {
        $unwind: '$submanagement',
      },
      {
        $unwind: '$role',
      },
    ];
  }
}
