import { Module } from '@nestjs/common';
import { SubManagementController } from './sub-management.controller';
import { SubManagementService } from './sub-management.service';
import { SubManagementTeamWorkRepositoryModule } from '@shinrai-way-of-work/core';

@Module({
  controllers: [SubManagementController],
  providers: [SubManagementService],
  imports: [SubManagementTeamWorkRepositoryModule.register()],
})
export class SubManagementModule {}
