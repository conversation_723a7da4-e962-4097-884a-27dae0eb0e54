import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { SubManagementTeamWorkRepository } from '@shinrai-way-of-work/core';
import {
  CustomJwtEntity,
  IShared,
  SubManagementQueryDto,
  ISubManagement,
} from '@shinrai-way-of-work/models';
import { ApiResponse, doTryCatch, filterObjectToQuery } from '@shinrai-way-of-work/utils';
import { Types } from 'mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Injectable()
export class SubManagementService {
  constructor(
    private readonly subManagementTeamWorkRepository: SubManagementTeamWorkRepository,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  async findAll(query: SubManagementQueryDto) {
    return doTryCatch(this.logger, async () => {
      const queryObject = filterObjectToQuery({ ...query, is_active: true }, {});
      const data = await this.subManagementTeamWorkRepository.find({
        query: queryObject,
      });

      return ApiResponse.True({ data, statusCode: HttpStatus.CREATED });
    });
  }

  async create(data: ISubManagement.Create, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      this.logger.debug(`Creating new subarea ${JSON.stringify(data)}`);

      const areaExists = await this.subManagementTeamWorkRepository.findById({
        id: data.subarea_id,
      });
      if (!areaExists) {
        return ApiResponse.False({
          message: 'La subgerencia asociada no existe',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }

      const result = await this.subManagementTeamWorkRepository.create({
        ...data,
        subarea_id: new Types.ObjectId(data.subarea_id),
        created_by: {
          email: user.email,
          name: user.unique_name,
          identifier: user.primarysid,
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async update(dto: IShared.RequestIdDto, data: ISubManagement.Update, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;

      const result = await this.subManagementTeamWorkRepository.update({
        query: { _id: id },
        data: {
          $set: {
            ...data,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        },
      });
      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async delete(dto: IShared.RequestIdDto, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;
      const exist = await this.subManagementTeamWorkRepository.findOne({
        query: { _id: id },
      });
      if (!exist) {
        return ApiResponse.False({
          message: 'Sub-management not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      await this.subManagementTeamWorkRepository.update({
        query: { _id: id },
        data: {
          $set: {
            is_active: false,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        },
      });

      return ApiResponse.True({ statusCode: HttpStatus.OK });
    });
  }
}
