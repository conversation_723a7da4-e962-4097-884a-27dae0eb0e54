import * as SDK from '@team_yumi/node-sdk';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { QueryValidationInterceptor } from '@shinrai-way-of-work/utils';
import { SubManagementService } from './sub-management.service';
import {
  CustomJwtEntity,
  IShared,
  ISubManagement,
  SubManagementQueryDto,
} from '@shinrai-way-of-work/models';
import { createZodDto } from 'nestjs-zod';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@SDK.Lib.Nest.Modules.Jwt.Authenticated()
@ApiBearerAuth('defaultBearerAuth')
@ApiTags('sub-management')
@Controller('sub-management')
export class SubManagementController {
  constructor(private readonly subManagementService: SubManagementService) {}

  @Get('')
  @ApiOperation({ summary: 'Get all sub-management' })
  @ApiResponse({ status: 200, description: 'Get all sub-management' })
  @UseInterceptors(new QueryValidationInterceptor(SubManagementQueryDto))
  async find(@Query() query: SubManagementQueryDto, @Res() res: Response<unknown>) {
    const result = await this.subManagementService.findAll(query);
    return res.status(result.statusCode).json(result);
  }

  @Post('')
  @ApiOperation({ summary: 'Create a new submanagement' })
  @ApiResponse({ status: 201, description: 'Submanagement created' })
  @ApiBody({ type: createZodDto(SubManagementQueryDto), required: true })
  async create(
    @Body(new ZodValidationPipe(SubManagementQueryDto)) data: ISubManagement.Create,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.subManagementService.create(data, user);
    return res.status(result.statusCode).json(result);
  }

  @Patch('/:id')
  @ApiOperation({ summary: 'Update submanagement by id' })
  @ApiResponse({ status: 200, description: 'Update submanagement by id' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'submanagement id',
    type: 'string',
  })
  @ApiBody({ type: createZodDto(SubManagementQueryDto), required: true })
  async update(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(SubManagementQueryDto)) data: ISubManagement.Update,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.subManagementService.update(id, data, user);
    return res.status(result.statusCode).json(result);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a sub-management' })
  @ApiResponse({ status: 200, description: 'Return the deleted sub-management ' })
  async delete(
    @Param() id: IShared.RequestIdDto,
    @Res() res: Response<unknown>,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity
  ) {
    const result = await this.subManagementService.delete(id, user);
    return res.status(result.statusCode).json(result);
  }
}
