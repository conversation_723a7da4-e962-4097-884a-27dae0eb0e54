import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { HolidayTeamWorkRepository } from '@shinrai-way-of-work/core';
import {
  HolidayQueryDto,
  CustomJwtEntity,
  IHoliday,
  IShared,
  WorkingDayQueryDto,
} from '@shinrai-way-of-work/models';
import { ApiResponse, doTryCatch, filterObjectToQuery } from '@shinrai-way-of-work/utils';
import {
  eachDayOfInterval,
  endOfMonth,
  format,
  getDay,
  isSameDay,
  isWeekend,
  parseISO,
} from 'date-fns';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { es } from 'date-fns/locale';

@Injectable()
export class HolidayService {
  constructor(
    private readonly holidayTeamWorkRepository: HolidayTeamWorkRepository,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  async findAll(query: HolidayQueryDto) {
    return doTryCatch(this.logger, async () => {
      const queryObject = filterObjectToQuery(query, {});
      const data = await this.holidayTeamWorkRepository.find({
        query: queryObject,
      });

      return ApiResponse.True({ data, statusCode: HttpStatus.CREATED });
    });
  }

  async findByCountry(dto: IShared.RequestIdDto, query: HolidayQueryDto) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;
      const queryObject = filterObjectToQuery(query, {});
      const data = await this.holidayTeamWorkRepository.find({
        query: { ...queryObject, country_id: id },
      });

      return ApiResponse.True({ data, statusCode: HttpStatus.CREATED });
    });
  }

  async getWorkingDaysByYear(dto: WorkingDayQueryDto) {
    return doTryCatch(this.logger, async () => {
      const year = Number(dto.year);

      const holidays = await this.holidayTeamWorkRepository.find({
        query: {},
        populate: { path: 'country_id', select: 'countryName' },
      });

      const holidaysByCountry = holidays.reduce((acc, h) => {
        if (!h.country_id || typeof h.country_id !== 'object' || !('countryName' in h.country_id))
          return acc;

        const key = h.country_id._id.toString();
        const holidayDate = typeof h.date === 'string' ? parseISO(h.date) : new Date(h.date);

        if (!acc[key]) acc[key] = [];
        acc[key].push(holidayDate);

        return acc;
      }, {} as Record<string, Date[]>);

      const countryNameMap = new Map<string, string>();
      holidays.forEach((h) => {
        if (!h.country_id || typeof h.country_id !== 'object' || !('countryName' in h.country_id))
          return;
        countryNameMap.set(h.country_id._id.toString(), h.country_id.countryName as string);
      });

      const result: {
        month: string;
        countries: Record<string, { workingDays: number; holidays: number }>;
      }[] = [];

      const yearlyTotals: Record<string, { workingDays: number; holidays: number }> = {};

      for (let month = 0; month < 12; month++) {
        const start = new Date(year, month, 1);
        const end = endOfMonth(start);
        const allDays = eachDayOfInterval({ start, end });

        const countriesData: Record<string, { workingDays: number; holidays: number }> = {};

        for (const [countryId, holidayDates] of Object.entries(holidaysByCountry)) {
          const workingDays = allDays.filter((day) => {
            const isHoliday = holidayDates.some(
              (h) => h.toISOString().split('T')[0] === day.toISOString().split('T')[0]
            );
            return !isWeekend(day) && !isHoliday;
          });

          const onlyMonthHolidays = holidayDates.filter(
            (h) => h.getFullYear() === year && h.getMonth() === month
          );

          const name = countryNameMap.get(countryId) ?? countryId;

          countriesData[name] = {
            workingDays: workingDays.length,
            holidays: onlyMonthHolidays.length,
          };

          if (!yearlyTotals[name]) {
            yearlyTotals[name] = { workingDays: 0, holidays: 0 };
          }

          yearlyTotals[name].workingDays += workingDays.length;
          yearlyTotals[name].holidays += onlyMonthHolidays.length;
        }

        // 🔽 Ordenar países por nombre alfabéticamente para este mes
        const countriesDataSorted = Object.fromEntries(
          Object.entries(countriesData).sort(([a], [b]) => a.localeCompare(b))
        );

        result.push({
          month: start.toLocaleString('es-MX', { month: 'long' }),
          countries: countriesDataSorted,
        });
      }

      // 🔽 Ordenar yearlyTotals por nombre de país
      const yearlyTotalsSorted = Object.fromEntries(
        Object.entries(yearlyTotals).sort(([a], [b]) => a.localeCompare(b))
      );

      return ApiResponse.True({
        data: {
          monthlyTotals: result,
          yearlyTotals: yearlyTotalsSorted,
        },
        statusCode: HttpStatus.OK,
      });
    });
  }

  async create(data: IHoliday.Create, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      this.logger.debug(`Creating new holiday ${JSON.stringify(data)}`);

      const parseDate = new Date(data.date);
      const targetYear = parseDate.getFullYear();
      const targetMonth = parseDate.getMonth();
      const targetDate = parseDate.getDate();

      const holidaysByCountry = await this.holidayTeamWorkRepository.find({
        query: { country_id: data.country_id },
      });

      const existingHoliday = holidaysByCountry.find((holiday) => {
        const holidayDate = new Date(holiday.date);
        return (
          holidayDate.getFullYear() === targetYear &&
          holidayDate.getMonth() === targetMonth &&
          holidayDate.getDate() === targetDate
        );
      });

      if (existingHoliday) {
        return ApiResponse.False({
          message: 'Holiday already exists for this country and date',
          statusCode: HttpStatus.BAD_REQUEST,
        });
      }

      const result = await this.holidayTeamWorkRepository.create({
        ...data,
        country_id: data.country_id,
        created_by: {
          email: user.email,
          name: user.unique_name,
          identifier: user.primarysid,
        },
      });

      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async update(dto: IShared.RequestIdDto, data: IHoliday.Update, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;

      const result = await this.holidayTeamWorkRepository.update({
        query: { _id: id },
        data: {
          $set: {
            ...data,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        },
      });
      return ApiResponse.True({ data: result, statusCode: HttpStatus.CREATED });
    });
  }

  async delete(dto: IShared.RequestIdDto, user: CustomJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;
      const exist = await this.holidayTeamWorkRepository.findOne({
        query: { _id: id },
      });
      if (!exist) {
        return ApiResponse.False({
          message: 'Holiday not found',
          statusCode: HttpStatus.NOT_FOUND,
        });
      }

      await this.holidayTeamWorkRepository.delete(id);

      return ApiResponse.True({ statusCode: HttpStatus.OK });
    });
  }
}
