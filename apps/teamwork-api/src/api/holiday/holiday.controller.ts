import * as SDK from '@team_yumi/node-sdk';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { QueryValidationInterceptor } from '@shinrai-way-of-work/utils';
import { HolidayService } from './holiday.service';
import {
  CustomJwtEntity,
  HolidayQueryDto,
  IHoliday,
  IShared,
  WorkingDayQueryDto,
} from '@shinrai-way-of-work/models';
import { createZodDto } from 'nestjs-zod';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@SDK.Lib.Nest.Modules.Jwt.Authenticated()
@ApiBearerAuth('defaultBearerAuth')
@ApiTags('holiday')
@Controller('holiday')
export class HolidayController {
  constructor(private readonly holidayService: HolidayService) {}

  @Get('')
  @ApiOperation({ summary: 'Get all holidays' })
  @ApiResponse({ status: 200, description: 'Get all holidays' })
  @UseInterceptors(new QueryValidationInterceptor(HolidayQueryDto))
  async find(@Query() query: HolidayQueryDto, @Res() res: Response<unknown>) {
    const result = await this.holidayService.findAll(query);
    return res.status(result.statusCode).json(result);
  }

  @Get('/country/:id')
  @ApiOperation({ summary: 'Get all holidays by country id' })
  @ApiResponse({ status: 200, description: 'Get all holidays by country id' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'country id',
    type: 'string',
  })
  async findByCountry(
    @Param() id: IShared.RequestIdDto,
    @Res() res: Response<unknown>,
    @Query() query: HolidayQueryDto
  ) {
    const result = await this.holidayService.findByCountry(id, query);
    return res.status(result.statusCode).json(result);
  }

  @Get('/working-days')
  @ApiOperation({ summary: 'Get working days by country and year' })
  @ApiResponse({ status: 200, description: 'Get working days by country and year' })
  @UseInterceptors(new QueryValidationInterceptor(WorkingDayQueryDto))
  async getWorkingDaysByCountry(@Query() query: WorkingDayQueryDto, @Res() res: Response<unknown>) {
    const result = await this.holidayService.getWorkingDaysByYear(query);
    return res.status(result.statusCode).json(result);
  }

  @Post('')
  @ApiOperation({ summary: 'Create a new holiday' })
  @ApiResponse({ status: 201, description: 'Holiday created' })
  @ApiBody({ type: createZodDto(HolidayQueryDto), required: true })
  async create(
    @Body(new ZodValidationPipe(HolidayQueryDto)) data: IHoliday.Create,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.holidayService.create(data, user);
    return res.status(result.statusCode).json(result);
  }

  @Patch('/:id')
  @ApiOperation({ summary: 'Update holiday by id' })
  @ApiResponse({ status: 200, description: 'Update holiday by id' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'holiday id',
    type: 'string',
  })
  @ApiBody({ type: createZodDto(HolidayQueryDto), required: true })
  async update(
    @Param() id: IShared.RequestIdDto,
    @Body(new ZodValidationPipe(HolidayQueryDto)) data: IHoliday.Update,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.holidayService.update(id, data, user);
    return res.status(result.statusCode).json(result);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a holiday' })
  @ApiResponse({ status: 200, description: 'Return the deleted holiday ' })
  async delete(
    @Param() id: IShared.RequestIdDto,
    @Res() res: Response<unknown>,
    @SDK.Lib.Nest.Modules.Jwt.User() user: CustomJwtEntity
  ) {
    const result = await this.holidayService.delete(id, user);
    return res.status(result.statusCode).json(result);
  }
}
