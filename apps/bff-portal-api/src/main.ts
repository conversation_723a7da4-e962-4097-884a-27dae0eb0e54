import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import * as express from 'express';
import { ApiModule } from './api/api.module';
import secrets from './config/secrets';
import { NestJS } from '@team_seki/winston-logger';

async function bootstrap() {
  const app = await NestFactory.create(ApiModule);
  app.useLogger(app.get(NestJS.NestLogger));
  app.enableCors();
  app.use(express.json({ limit: '50mb' }));
  const port = secrets.PORT_BFF_API || 8080;
  await app.listen(port);

  Logger.log(`🚀 Api is running on: http://localhost:${port}`, secrets.PRODUCT_NAME);
}

bootstrap();
