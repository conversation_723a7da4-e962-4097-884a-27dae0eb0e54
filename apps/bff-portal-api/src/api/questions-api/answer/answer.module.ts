import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AnswerController } from './answer.controller';
import { AnswerService } from './answer.service';
import secrets from '../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_QUESTIONS_API}/answer` })],
  controllers: [AnswerController],
  providers: [AnswerService],
})
export class AnswerModule {}
