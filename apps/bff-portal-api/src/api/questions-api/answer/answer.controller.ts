import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import {
  AnswerCreateDto,
  AnswerCreateSchema,
  AnswerUpdateDto,
  AnswerUpdateSchema,
} from '@shinrai-way-of-work/models';
import { AnswerService } from './answer.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@Controller('answer')
@Authenticated()
export class AnswerController {
  constructor(private readonly groupQuestionsFeatureService: AnswerService) {}

  @Post()
  async createAnswer(
    @Body(new ZodValidationPipe(AnswerCreateSchema))
    body: AnswerCreateDto,
    @Req() req: Request
  ) {
    return this.groupQuestionsFeatureService.createAnswer(body, req.headers);
  }

  @Get()
  async getAnswer(@Query() query: any, @Req() req: Request) {
    return this.groupQuestionsFeatureService.getAnswer(query, req.headers);
  }

  @Get(':id')
  async getAnswerById(@Param('id') id: string, @Req() req: Request) {
    return this.groupQuestionsFeatureService.getAnswerById(id, req.headers);
  }

  @Patch(':id')
  async updateAnswer(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(AnswerUpdateSchema))
    body: AnswerUpdateDto,
    @Req() req: Request
  ) {
    return this.groupQuestionsFeatureService.updateAnswer(id, body, req.headers);
  }

  @Delete(':id')
  async deleteAnswer(@Param('id') id: string, @Req() req: Request) {
    return this.groupQuestionsFeatureService.deleteAnswer(id, req.headers);
  }
}
