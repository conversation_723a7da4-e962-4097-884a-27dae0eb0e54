import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import secrets from '../../config/secrets';
import FormData from 'form-data';

@Injectable()
export class FileService {
  constructor(private readonly httpService: HttpService) {}

  async uploadFile(file: Express.Multer.File, data: any, token: string) {
    const form = new FormData();
    form.append(file.fieldname, file.buffer, {
      filename: file.originalname,
      contentType: file.mimetype,
    });

    const keys = Object.keys(data);
    keys.forEach((key) => {
      form.append(key, data[key]);
    });

    return pipeRequest(
      this.httpService.post(`${secrets.URL_FILE_MANAGER_API}/file/upload`, form, {
        headers: { authorization: token },
      })
    );
  }

  async getFile(path: string, token: string) {
    return pipeRequest(
      this.httpService.get(`${secrets.URL_FILE_MANAGER_API}/file`, {
        headers: { authorization: token },
        params: { path },
      })
    );
  }

  async getAllFiles(token: string) {
    return pipeRequest(
      this.httpService.get(`${secrets.URL_FILE_MANAGER_API}/file/all`, {
        headers: { authorization: token },
      })
    );
  }

  async deleteFile(path: string, token: string) {
    return pipeRequest(
      this.httpService.delete(`${secrets.URL_FILE_MANAGER_API}/file`, {
        headers: { authorization: token },
        params: { path },
      })
    );
  }
}
