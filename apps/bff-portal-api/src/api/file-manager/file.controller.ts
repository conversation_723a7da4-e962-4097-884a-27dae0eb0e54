import { Body, Controller, Delete, Get, Post, Query, UploadedFile, UseInterceptors } from "@nestjs/common";
import { FileInterceptor } from '@nestjs/platform-express';
import * as SDK from '@team_yumi/node-sdk';
import { HeaderAuthToken } from "@shinrai-way-of-work/core";
import { FileService } from "./file.service";


@SDK.Lib.Nest.Modules.Jwt.Authenticated()
@Controller('file')
export class FileController {
  
  constructor(
    private readonly fileService: FileService
  ) {}

  @Get('')
  async getByPath(
    @Query('path') path: string,
    @HeaderAuthToken() token: string
  ) {
    return await this.fileService.getFile(path, token);
  }

  @Get('all')
  async getAllFiles(
    @HeaderAuthToken() token: string
  ) {
    return await this.fileService.getAllFiles(token);
  }


  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @HeaderAuthToken() token: string
  ) {
    return await this.fileService.uploadFile(file, body, token);
  }

  @Delete('')
  async deleteFile(
    @Query('path') path: string,
    @HeaderAuthToken() token: string
  ) {
    return await this.fileService.deleteFile(path, token);
  }





}