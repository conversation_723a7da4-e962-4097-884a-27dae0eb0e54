// import { <PERSON>, Get, Param, Req } from "@nestjs/common";
// import { Request } from 'express';
// import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
// import { ParamsService } from "./params.service";

// @Controller('products-api/params')
// @Authenticated()
// export class ParamsController {
//   constructor(
//     private readonly paramsService: ParamsService
//   ) {}

//   @Get(':key')
//   getParams(@Param('key') key: string, @Req() req: Request) {
//     return this.paramsService.getParams(req.headers, key);
//   }
// }