// import { HttpService } from "@nestjs/axios";
// import { Injectable } from "@nestjs/common";
// import {pipeRequest} from '@shinrai-way-of-work/utils'
// import secrets from "apps/bff-portal-api/src/config/secrets";
// import { IncomingHttpHeaders } from "http";
// @Injectable()
// export class ParamsService {
//   private baseUrl = secrets.URL_PRODUCTS_API;
//   private baseHeaders = {
//     headers: {}
//   }
//   constructor(
//     private readonly httpService: HttpService
//   ) {}

//   getParams(headers: IncomingHttpHeaders, key: string) {
//     const url = `${this.baseUrl}/v1/params/${key}`;
//     this.baseHeaders.headers = headers;
//     return pipeRequest(this.httpService.get(url, this.baseHeaders));
//   }
// }