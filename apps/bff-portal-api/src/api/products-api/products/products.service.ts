// import { HttpService } from "@nestjs/axios";
// import { Injectable } from "@nestjs/common";
// import { ProductCreateRequestDto, ProductFilterRequestDto, ProductRequestDto } from "@shinrai-way-of-work/models";
// import { pipeRequest } from "@shinrai-way-of-work/utils";
// import secrets from "apps/bff-portal-api/src/config/secrets";
// import { IncomingHttpHeaders } from "http";

// @Injectable()
// export class ProductsService {
//   private baseUrl = secrets.URL_PRODUCTS_API;
//   private baseHeaders = {
//     headers: {}
//   }

//   constructor(private readonly httpService: HttpService) {}

//   createProduct(body: ProductCreateRequestDto, headers: IncomingHttpHeaders) {
//     const url = `${this.baseUrl}/v1/products`;
//     this.baseHeaders.headers = { 
//       'Content-Type': 'application/json',
//       'Authorization': headers['authorization']
//      };
//     return pipeRequest(this.httpService.post(url, body, this.baseHeaders));
//   }

//   getAllProducts(query: ProductFilterRequestDto, headers: IncomingHttpHeaders) {
//     const url = `${this.baseUrl}/v1/products`;
//     this.baseHeaders.headers = headers;
//     return pipeRequest(this.httpService.get(url, { params: query, ...this.baseHeaders }));
//   }

//   getProduct(dto: ProductRequestDto, headers: IncomingHttpHeaders) {
//     const url = `${this.baseUrl}/v1/products/${dto.id}`;
//     this.baseHeaders.headers = headers;
//     return pipeRequest(this.httpService.get(url, this.baseHeaders));
//   }

//   getProductsReport(query: ProductFilterRequestDto, headers: IncomingHttpHeaders) {
//     const url = `${this.baseUrl}/v1/products/report`;
//     this.baseHeaders.headers = headers;
//     return pipeRequest(this.httpService.get(url, { params: query, ...this.baseHeaders }));
//   }

//   deleteProduct(dto: ProductRequestDto, headers: IncomingHttpHeaders) {
//     const url = `${this.baseUrl}/v1/products/${dto.id}`;
//     this.baseHeaders.headers = headers;
//     return pipeRequest(this.httpService.delete(url, this.baseHeaders));
//   }
// }