// import { Request } from 'express';
// import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
// import { Body, Controller, Delete, Get, Param, Post, Query, Req } from "@nestjs/common";
// import { ProductsService } from "./products.service";
// import { ProductCreateRequestDto, ProductFilterRequestDto, ProductRequestDto } from "@shinrai-way-of-work/models";

// @Controller('products-api')
// @Authenticated()
// export class ProductsController {
//   constructor(
//     private readonly productsService: ProductsService
//   ) {}

//   @Post('products')
//   async postProducts(@Body() body: ProductCreateRequestDto,  @Req() req: Request){
//     return this.productsService.createProduct(body, req.headers);
//   }

//   @Get('products')
//   async getAllProducts(@Query() query: ProductFilterRequestDto, @Req() req: Request){
//     return this.productsService.getAllProducts(query, req.headers);
//   }

//   @Get('products/:id')
//   async getProduct( @Param() dto: ProductRequestDto,  @Req() req: Request){
//     return this.productsService.getProduct(dto, req.headers);
//   }

//   @Get('products/report')
//   async getProductsReport(@Query() query: ProductFilterRequestDto, @Req() req: Request){
//     return this.productsService.getProductsReport(query, req.headers);
//   }

//   @Delete('products/:id')
//   async deleteProduct( @Param() dto: ProductRequestDto,  @Req() req: Request){
//     return this.productsService.getProduct(dto, req.headers);
//   }
// }