import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import secrets from '../../config/secrets';
import { HeroNewsService } from './hero-news.service';
import { HeroNewsController } from './hero-news.controller';
import { FileService } from '../file-manager/file.service';

@Module({
  imports: [HttpModule.register({ baseURL: secrets.URL_HERO_NEWS_API })],
  controllers: [HeroNewsController],
  providers: [HeroNewsService, FileService],
})
export class HeroNewsModule {}
