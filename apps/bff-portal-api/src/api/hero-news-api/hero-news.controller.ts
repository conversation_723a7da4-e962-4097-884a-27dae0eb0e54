import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import {
  HeroNewsCreateDTO,
  HeroNewsCreateSchema,
  HeroNewsUpdateDTO,
  HeroNewsUpdateSchema,
} from '@shinrai-way-of-work/models';
import { HeroNewsService } from './hero-news.service';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { FileService } from '../file-manager/file.service';
import { HeaderAuthToken, ZodValidationPipe } from '@shinrai-way-of-work/core';
import { lastValueFrom } from 'rxjs';

@Controller('hero-news')
@Authenticated()
export class HeroNewsController {
  constructor(
    private readonly heroNewsService: HeroNewsService,
    private fileService: FileService
  ) {}

  @Post()
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'imagen', maxCount: 1 },
      { name: 'banner', maxCount: 1 },
    ])
  )
  async createHeroNews(
    @Body(new ZodValidationPipe(HeroNewsCreateSchema)) body: HeroNewsCreateDTO,
    @Req() req: Request,
    @UploadedFiles() files: { imagen: Express.Multer.File[]; banner: Express.Multer.File[] },
    @HeaderAuthToken() token: string
  ) {
    if (files) {
      if (files.imagen.length > 0) {
        files.imagen[0].fieldname = 'file';
        const response = await lastValueFrom(
          await this.fileService.uploadFile(
            files.imagen[0],
            {
              is_public: 'true',
              folder_url: 'hero-news/images/',
            },
            token
          )
        );
        body.imagen = {
          filepath: response.data?.filepath,
          url: response.data?.url,
        };
      }
      if (files.banner?.length > 0) {
        files.banner[0].fieldname = 'file';
        const response = await lastValueFrom(
          await this.fileService.uploadFile(
            files.banner[0],
            {
              is_public: 'true',
              folder_url: 'hero-news/images/',
            },
            token
          )
        );
        body.banner = {
          filepath: response.data?.filepath,
          url: response.data?.url,
        };
      }
    }

    return this.heroNewsService.createHeroNews(body, req.headers);
  }

  @Get()
  async getHeroNews(@Query() query: any, @Req() req: Request) {
    return this.heroNewsService.getHeroNews(query, req.headers);
  }

  @Get(':id')
  async getHeroNewsById(@Param('id') id: string, @Req() req: Request) {
    return this.heroNewsService.getHeroNewsById(id, req.headers);
  }

  @Put(':id')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'imagen', maxCount: 1 },
      { name: 'banner', maxCount: 1 },
    ])
  )
  async updateHeroNews(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(HeroNewsUpdateSchema)) body: HeroNewsUpdateDTO,
    @Req() req: Request,
    @UploadedFiles() files: { imagen: Express.Multer.File[]; banner: Express.Multer.File[] },
    @HeaderAuthToken() token: string
  ) {
    if (files) {
      if (files.imagen?.length > 0) {
        files.imagen[0].fieldname = 'file';
        const response = await lastValueFrom(
          await this.fileService.uploadFile(
            files.imagen[0],
            {
              is_public: 'true',
              folder_url: 'hero-news/images/',
            },
            token
          )
        );
        body.imagen = {
          filepath: response.data?.filepath,
          url: response.data?.url,
        };
      }
      if (files.banner?.length > 0) {
        files.banner[0].fieldname = 'file';
        const response = await lastValueFrom(
          await this.fileService.uploadFile(
            files.banner[0],
            {
              is_public: 'true',
              folder_url: 'hero-news/images/',
            },
            token
          )
        );
        body.banner = {
          filepath: response.data?.filepath,
          url: response.data?.url,
        };
      }
    }
    return this.heroNewsService.updateHeroNews(id, body, req.headers);
  }

  @Delete(':id')
  async deleteHeroNews(@Param('id') id: string, @Req() req: Request) {
    return this.heroNewsService.deleteHeroNews(id, req.headers);
  }
}
