import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { HeroNewsCreateDTO, HeroNewsUpdateDTO } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class HeroNewsService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  createHeroNews(body: HeroNewsCreateDTO, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.post('/hero-news', body, this.baseHeaders));
  }

  // Read all HeroNews
  getHeroNews(query: any, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(
      this.httpService.get('/hero-news', { params: query, headers: this.baseHeaders.headers })
    );
  }

  getHeroNewsById(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(
      this.httpService.get(`/hero-news/${id}`, { headers: this.baseHeaders.headers })
    );
  }

  updateHeroNews(id: string, body: HeroNewsUpdateDTO, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.put(`/hero-news/${id}`, body, this.baseHeaders));
  }

  deleteHeroNews(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(
      this.httpService.delete(`/hero-news/${id}`, { headers: this.baseHeaders.headers })
    );
  }
}
