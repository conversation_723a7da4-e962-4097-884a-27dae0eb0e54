import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { UnitMeasureCreateDto, UnitMeasureUpdateDto } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class UnitMeasureService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  createUnitMeasure(body: UnitMeasureCreateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.post('', body, this.baseHeaders));
  }

  getUnitMeasures(query: any, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(
      this.httpService.get('', { params: query, headers: this.baseHeaders.headers })
    );
  }

  getUnitMeasureById(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.get(`/${id}`, { headers: this.baseHeaders.headers }));
  }

  updateUnitMeasure(id: string, body: UnitMeasureUpdateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.patch(`/${id}`, body, this.baseHeaders));
  }

  deleteUnitMeasure(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.delete(`/${id}`, { headers: this.baseHeaders.headers }));
  }
}
