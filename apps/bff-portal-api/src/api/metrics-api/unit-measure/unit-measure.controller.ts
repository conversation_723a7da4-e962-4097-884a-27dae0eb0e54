import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import {
  UnitMeasureCreateDto,
  UnitMeasureCreateSchema,
  UnitMeasureUpdateDto,
  UnitMeasureUpdateSchema,
} from '@shinrai-way-of-work/models';
import { UnitMeasureService } from './unit-measure.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@Controller('unit-measure')
@Authenticated()
export class UnitMeasureController {
  constructor(private readonly unitMeasureService: UnitMeasureService) {}

  @Post()
  async createUnitMeasure(
    @Body(new ZodValidationPipe(UnitMeasureCreateSchema)) body: UnitMeasureCreateDto,
    @Req() req: Request
  ) {
    return this.unitMeasureService.createUnitMeasure(body, req.headers);
  }

  @Get()
  async getUnitMeasures(@Query() query: any, @Req() req: Request) {
    return this.unitMeasureService.getUnitMeasures(query, req.headers);
  }

  @Get(':id')
  async getUnitMeasureById(@Param('id') id: string, @Req() req: Request) {
    return this.unitMeasureService.getUnitMeasureById(id, req.headers);
  }

  @Patch(':id')
  async updateUnitMeasure(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(UnitMeasureUpdateSchema)) body: UnitMeasureUpdateDto,
    @Req() req: Request
  ) {
    return this.unitMeasureService.updateUnitMeasure(id, body, req.headers);
  }

  @Delete(':id')
  async deleteUnitMeasure(@Param('id') id: string, @Req() req: Request) {
    return this.unitMeasureService.deleteUnitMeasure(id, req.headers);
  }
}
