import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { UnitMeasureController } from './unit-measure.controller';
import { UnitMeasureService } from './unit-measure.service';
import secrets from '../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_NEW_METRIC_API}/unit-measure` })],
  controllers: [UnitMeasureController],
  providers: [UnitMeasureService],
})
export class UnitMeasureModule {}
