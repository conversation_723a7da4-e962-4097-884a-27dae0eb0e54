import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { MetricController } from './metric.controller';
import { MetricService } from './metric.service';
import secrets from '../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_NEW_METRIC_API}/metrics` })],
  controllers: [MetricController],
  providers: [MetricService],
})
export class MetricModule {}
