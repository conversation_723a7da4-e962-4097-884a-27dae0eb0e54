import { Request, Response } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, Res } from '@nestjs/common';
import {
  MetricCreateDto,
  MetricCreateSchema,
  MetricUpdateDto,
  MetricUpdateSchema,
} from '@shinrai-way-of-work/models';
import { MetricService } from './metric.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@Controller('metric')
@Authenticated()
export class MetricController {
  constructor(private readonly metricService: MetricService) {}

  @Post()
  async createMetric(
    @Body(new ZodValidationPipe(MetricCreateSchema))
    body: MetricCreateDto,
    @Req() req: Request
  ) {
    return this.metricService.createMetric(body, req.headers);
  }

  @Get()
  async getMetrics(@Query() query: any, @Req() req: Request) {
    return this.metricService.getMetrics(query, req.headers);
  }

  @Get('/export/xlsx')
  async getMetricValuesExcel(@Query() query: any, @Req() req: Request, @Res() res: Response) {
    return this.metricService.getMetricExcel(query, req.headers, res);
  }

  @Get(':id')
  async getMetricById(@Param('id') id: string, @Req() req: Request) {
    return this.metricService.getMetricById(id, req.headers);
  }

  @Patch(':id')
  async updateMetric(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(MetricUpdateSchema))
    body: MetricUpdateDto,
    @Req() req: Request
  ) {
    return this.metricService.updateMetric(id, body, req.headers);
  }

  @Delete(':id')
  async deleteMetric(@Param('id') id: string, @Req() req: Request) {
    return this.metricService.deleteMetric(id, req.headers);
  }
}
