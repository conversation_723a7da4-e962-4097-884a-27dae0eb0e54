import { Response } from 'express';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { MetricCreateDto, MetricUpdateDto } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class MetricService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  createMetric(body: MetricCreateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.post('', body, this.baseHeaders));
  }

  getMetrics(query: any, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(
      this.httpService.get('', { params: query, headers: this.baseHeaders.headers })
    );
  }

  getMetricById(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.get(`/${id}`, { headers: this.baseHeaders.headers }));
  }

  async getMetricExcel(query: any, headers: IncomingHttpHeaders, res: Response) {
    this.baseHeaders.headers = {
      Authorization: headers['authorization'],
    };

    try {
      const response = await firstValueFrom(
        this.httpService.get('/export/xlsx', {
          params: query,
          headers: this.baseHeaders.headers,
          responseType: 'arraybuffer',
        })
      );

      const buffer = response.data;

      if (!buffer || buffer.byteLength === 0) {
        res.status(404).send('No se encontraron datos para descargar.');
        return;
      }

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.send(buffer);
    } catch (err) {
      res.status(500).send('Error al generar el archivo.');
    }
  }

  updateMetric(id: string, body: MetricUpdateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.patch(`/${id}`, body, this.baseHeaders));
  }

  deleteMetric(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.delete(`/${id}`, { headers: this.baseHeaders.headers }));
  }
}
