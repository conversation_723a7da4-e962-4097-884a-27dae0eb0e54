import { Response } from 'express';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, Res } from '@nestjs/common';
import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import {
  MetricValueCreateArraySchema,
  MetricValueCreateDto,
  MetricValueUpdateDto,
  MetricValueUpdateSchema,
} from '@shinrai-way-of-work/models';
import { MetricValueService } from './metric-value.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@Controller('metric-value')
@Authenticated()
export class MetricValueController {
  constructor(private readonly metricValueService: MetricValueService) {}

  @Post()
  async createMetricValue(
    @Body(new ZodValidationPipe(MetricValueCreateArraySchema)) body: MetricValueCreateDto,
    @Req() req: Request
  ) {
    return this.metricValueService.createMetricValue(body, req.headers);
  }

  @Get()
  async getMetricValues(@Query() query: any, @Req() req: Request) {
    return this.metricValueService.getMetricValues(query, req.headers);
  }

  @Get('/export/xlsx')
  async getMetricValuesExcel(@Query() query: any, @Req() req: Request, @Res() res: Response) {
    return this.metricValueService.getMetricValuesExcel(query, req.headers, res);
  }

  @Get(':id')
  async getMetricValueById(@Param('id') id: string, @Req() req: Request) {
    return this.metricValueService.getMetricValueById(id, req.headers);
  }

  @Patch(':id')
  async updateMetricValue(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(MetricValueUpdateSchema)) body: MetricValueUpdateDto,
    @Req() req: Request
  ) {
    return this.metricValueService.updateMetricValue(id, body, req.headers);
  }

  @Delete(':id')
  async deleteMetricValue(@Param('id') id: string, @Req() req: Request) {
    return this.metricValueService.deleteMetricValue(id, req.headers);
  }
}
