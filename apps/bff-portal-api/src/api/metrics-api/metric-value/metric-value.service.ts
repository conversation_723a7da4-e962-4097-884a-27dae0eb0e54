import { Response } from 'express';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { MetricValueCreateDto, MetricValueUpdateDto } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';
import { catchError, firstValueFrom, map, of, tap } from 'rxjs';

@Injectable()
export class MetricValueService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  createMetricValue(body: MetricValueCreateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.post('', body, this.baseHeaders));
  }

  getMetricValues(query: any, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(
      this.httpService.get('', { params: query, headers: this.baseHeaders.headers })
    );
  }

  async getMetricValuesExcel(query: any, headers: IncomingHttpHeaders, res: Response) {
    this.baseHeaders.headers = {
      Authorization: headers['authorization'],
    };

    try {
      const response = await firstValueFrom(
        this.httpService.get('/export/xlsx', {
          params: query,
          headers: this.baseHeaders.headers,
          responseType: 'arraybuffer',
        })
      );

      const buffer = response.data;

      if (!buffer || buffer.byteLength === 0) {
        res.status(404).send('No se encontraron datos para descargar.');
        return;
      }

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.send(buffer);
    } catch (err) {
      res.status(500).send('Error al generar el archivo.');
    }
  }

  getMetricValueById(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.get(`/${id}`, { headers: this.baseHeaders.headers }));
  }

  updateMetricValue(id: string, body: MetricValueUpdateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.patch(`/${id}`, body, this.baseHeaders));
  }

  deleteMetricValue(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.delete(`/${id}`, { headers: this.baseHeaders.headers }));
  }
}
