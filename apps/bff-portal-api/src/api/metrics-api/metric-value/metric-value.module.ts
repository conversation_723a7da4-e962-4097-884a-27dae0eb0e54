import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { MetricValueController } from './metric-value.controller';
import { MetricValueService } from './metric-value.service';
import secrets from '../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_NEW_METRIC_API}/metric-values` })],
  controllers: [MetricValueController],
  providers: [MetricValueService],
})
export class MetricValueModule {}
