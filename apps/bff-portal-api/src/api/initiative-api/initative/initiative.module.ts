import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { InitiativeController } from './initiative.controller';
import { InitiativeService } from './initiative.service';
import secrets from '../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_INITIATIVES_API}/initiative` })],
  controllers: [InitiativeController],
  providers: [InitiativeService],
})
export class InitiativeModule {}
