import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query, Req } from '@nestjs/common';
import {
  InitiativeCreateDto,
  InitiativeCreateSchema,
  InitiativeUpdateDto,
  InitiativeUpdateSchema,
} from '@shinrai-way-of-work/models';
import { InitiativeService } from './initiative.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@Controller('initiative')
@Authenticated()
export class InitiativeController {
  constructor(private readonly initiativeService: InitiativeService) {}

  @Post()
  async createInitiative(
    @Body(new ZodValidationPipe(InitiativeCreateSchema))
    body: InitiativeCreateDto,
    @Req() req: Request
  ) {
    return this.initiativeService.createInitiative(body, req.headers);
  }

  @Get()
  async getInitiative(@Query() query: any, @Req() req: Request) {
    return this.initiativeService.getInitiative(query, req.headers);
  }

  @Get('filters')
  async getInitiativeItLeadAndItDirector(@Req() req: Request) {
    return this.initiativeService.getInitiativeItleadAndItDirector(req.headers);
  }

  @Get('count')
  async getInitiativeCount(@Query() query:any, @Req() req: Request) {
    return this.initiativeService.getInitiativeCount(query, req.headers);
  }

  @Get(':id')
  async getInitiativeById(@Param('id') id: string, @Req() req: Request) {
    return this.initiativeService.getInitiativeById(id, req.headers);
  }

  @Patch(':id')
  async updateInitiative(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(InitiativeUpdateSchema))
    body: InitiativeUpdateDto,
    @Req() req: Request
  ) {
    return this.initiativeService.updateInitiative(id, body, req.headers);
  }

  @Delete(':id')
  async deleteInitiative(@Param('id') id: string, @Req() req: Request) {
    return this.initiativeService.deleteInitiative(id, req.headers);
  }
}
