import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { InitiativeCreateDto, InitiativeUpdateDto } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class InitiativeService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  createInitiative(body: InitiativeCreateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.post('', body, this.baseHeaders));
  }

  getInitiative(query: any, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(
      this.httpService.get('', { params: query, headers: this.baseHeaders.headers })
    );
  }

  getInitiativeItleadAndItDirector(headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.get(`/filters`, { headers: this.baseHeaders.headers }));
  }

  getInitiativeById(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.get(`/${id}`, { headers: this.baseHeaders.headers }));
  }

  getInitiativeCount(query:any, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.get(`/count`, { params: query, headers: this.baseHeaders.headers }));
  }

  updateInitiative(id: string, body: InitiativeUpdateDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.patch(`/${id}`, body, this.baseHeaders));
  }

  deleteInitiative(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.delete(`/${id}`, { headers: this.baseHeaders.headers }));
  }
}
