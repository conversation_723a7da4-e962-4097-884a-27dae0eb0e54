import { Body, Controller, Get, Param, Post, Req } from "@nestjs/common";
import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { GraphService } from "./graph.service";
import { ZodValidationPipe } from "@shinrai-way-of-work/core";
import { GraphComparativeQueryDto, GraphComparativeQuerySchema } from "@shinrai-way-of-work/models";


@Controller('initiative/graph')
@Authenticated()
export class GraphController {
  constructor(private readonly graphService: GraphService) {}

  @Post('comparative')
  async createGraphComparativeData(
    @Body(new ZodValidationPipe(GraphComparativeQuerySchema))
    body: GraphComparativeQueryDto,
    @Req() req: Request
  ) {
    return this.graphService.createGraphComparativeData(body, req.headers);
  }

  @Get('comparative/filters')
  async getFilterData(
    @Req() req: Request
  ) {
    return this.graphService.getFilterData(req.headers);
  }

  @Get('comparative/feature/:id')
  async getFeatureData(
    @Req() req: Request,
    @Param('id') id: string
  ) {
    return this.graphService.getFeatureData(req.headers, id);
  }
}