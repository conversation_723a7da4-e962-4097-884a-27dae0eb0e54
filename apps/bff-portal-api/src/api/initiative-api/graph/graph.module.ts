import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import secrets from '../../../config/secrets';
import { GraphController } from './graph.controller';
import { GraphService } from './graph.service';



@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_INITIATIVES_API}/graph` })],
  controllers: [GraphController],
  providers: [GraphService],
})
export class GraphModule {}