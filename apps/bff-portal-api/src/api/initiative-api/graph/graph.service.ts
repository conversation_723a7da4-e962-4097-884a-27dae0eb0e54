import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { GraphComparativeQueryDto } from "@shinrai-way-of-work/models";
import { pipeRequest } from "@shinrai-way-of-work/utils";
import { IncomingHttpHeaders } from "http";

@Injectable()
export class GraphService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}


  createGraphComparativeData(body: GraphComparativeQueryDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.post('/comparative', body, this.baseHeaders));
  } 

  getFilterData(headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.get('/comparative/filters', { headers: this.baseHeaders.headers }));
  }

  getFeatureData(headers: IncomingHttpHeaders, id: string) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.get(`/comparative/feature/${id}`, { headers: this.baseHeaders.headers }));
  }

}