import { Module } from '@nestjs/common';
import { FeatureModule } from './feature/feature.module';
import { InitiativeModule } from './initative/initiative.module';
import { CommentModule } from './comment/comment.module';
import { GraphModule } from './graph/graph.module';

@Module({
  imports: [FeatureModule, InitiativeModule, CommentModule, GraphModule],
  controllers: [],
  providers: [],
})
export class InitiativesModule {}
