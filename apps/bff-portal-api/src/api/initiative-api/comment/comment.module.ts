import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CommentController } from './comment.controller';
import { CommentService } from './comment.service';
import secrets from '../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_INITIATIVES_API}/comment` })],
  controllers: [CommentController],
  providers: [CommentService],
})
export class CommentModule {}
