import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import {
  CommentCreateDto,
  CommentCreateSchema,
  CommentUpdateDto,
  CommentUpdateSchema,
} from '@shinrai-way-of-work/models';
import { CommentService } from './comment.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@Controller('comment')
@Authenticated()
export class CommentController {
  constructor(private readonly commentService: CommentService) {}

  @Post()
  async createComment(
    @Body(new ZodValidationPipe(CommentCreateSchema))
    body: CommentCreateDto,
    @Req() req: Request
  ) {
    return this.commentService.createComment(body, req.headers);
  }

  @Get()
  async getComment(@Query() query: any, @Req() req: Request) {
    return this.commentService.getComment(query, req.headers);
  }

  @Get(':id')
  async getCommentById(@Param('id') id: string, @Req() req: Request) {
    return this.commentService.getCommentById(id, req.headers);
  }

  @Patch(':id')
  async updateComment(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(CommentUpdateSchema))
    body: CommentUpdateDto,
    @Req() req: Request
  ) {
    return this.commentService.updateComment(id, body, req.headers);
  }

  @Delete(':id')
  async deleteComment(@Param('id') id: string, @Req() req: Request) {
    return this.commentService.deleteComment(id, req.headers);
  }
}
