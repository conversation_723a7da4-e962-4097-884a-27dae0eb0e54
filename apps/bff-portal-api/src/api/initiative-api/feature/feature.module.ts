import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { FeatureController } from './feature.controller';
import { FeatureService } from './feature.service';
import secrets from '../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_INITIATIVES_API}/feature` })],
  controllers: [FeatureController],
  providers: [FeatureService],
})
export class FeatureModule {}
