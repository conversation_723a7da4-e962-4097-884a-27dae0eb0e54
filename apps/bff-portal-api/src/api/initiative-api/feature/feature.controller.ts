import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query, Req } from '@nestjs/common';
import {
  FeatureCreateDto,
  FeatureCreateSchema,
  FeatureUpdateDto,
  FeatureUpdateSchema,
} from '@shinrai-way-of-work/models';
import { FeatureService } from './feature.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';

@Controller('initiative/feature')
@Authenticated()
export class FeatureController {
  constructor(private readonly featureService: FeatureService) {}

  @Post()
  async createFeature(
    @Body(new ZodValidationPipe(FeatureCreateSchema))
    body: FeatureCreateDto,
    @Req() req: Request
  ) {
    return this.featureService.createFeature(body, req.headers);
  }

  @Get()
  async getFeature(@Query() query: any, @Req() req: Request) {
    return this.featureService.getFeature(query, req.headers);
  }

  @Get('/graph-valoration')
  async getFeatureGraphValoration(@Query() query: any, @Req() req: Request) {
    return this.featureService.getFeatureGraphValoration(query, req.headers);
  }

  @Get(':id')
  async getFeatureById(@Param('id') id: string, @Req() req: Request) {
    return this.featureService.getFeatureById(id, req.headers);
  }

  @Patch(':id')
  async updateFeature(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(FeatureUpdateSchema))
    body: FeatureUpdateDto,
    @Req() req: Request
  ) {
    return this.featureService.updateFeature(id, body, req.headers);
  }

  @Patch(':id/new-answer')
  async addAnswer(@Param('id') id: string, @Body() body: any, @Req() req: Request) {
    return this.featureService.addAnswer(id, body, req.headers);
  }

  @Delete(':id')
  async deleteFeature(@Param('id') id: string, @Req() req: Request) {
    return this.featureService.deleteFeature(id, req.headers);
  }
}
