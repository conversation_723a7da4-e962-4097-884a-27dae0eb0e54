import { IncomingHttpHeaders } from 'http';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import secrets from '../../../config/secrets';
import { CreateResponseDto } from '@shinrai-way-of-work/models';

@Injectable()
export class SurveyService {
  private baseUrl = secrets.URL_METRICS_API;
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  getTeamsByEmail(email: string, headers: IncomingHttpHeaders) {
    const url = `${this.baseUrl}/survey-collection/user/${email}`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }

  getScale(headers: IncomingHttpHeaders) {
    const url = `${this.baseUrl}/survey-collection/scale`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }

  getSurveyByType(type: string, headers: IncomingHttpHeaders) {
    const url = `${this.baseUrl}/survey-collection/type/${type}`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }

  getSurveyCompleted(
    surveyId: string,
    userId: string,
    teamId: string,
    headers: IncomingHttpHeaders
  ) {
    const url = `${this.baseUrl}/survey-collection/${surveyId}/user/${userId}/team/${teamId}`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }


  postSurveyResponse(body: CreateResponseDto, headers: IncomingHttpHeaders) {
    const url = `${this.baseUrl}/survey-collection/response`;
    this.baseHeaders.headers = { 
      'Content-Type': 'application/json',
      'Authorization': headers['authorization']
     };
    return pipeRequest(this.httpService.post(url, body, this.baseHeaders));
  }

  getResponseBySurvey(
    surveyId: string,
    userId: string,
    teamId: string,
    headers: IncomingHttpHeaders
  ) {
    const url = `${this.baseUrl}/survey-collection/response/user/${userId}/team/${teamId}/survey/${surveyId}`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }


}
