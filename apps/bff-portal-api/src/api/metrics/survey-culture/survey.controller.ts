import { Request } from 'express';
import { Body, Controller, Get, Param, Post, Req } from '@nestjs/common';
import { SurveyService } from './survey.service';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { CreateResponseDto } from '@shinrai-way-of-work/models';

@Controller('metrics/survey-collection')
@Authenticated()
export class SurveyController {
  constructor(private readonly surveyService: SurveyService) {}

  @Get('user/:email')
  getTeamsByEmail(@Param('email') email: string, @Req() req: Request) {
    return this.surveyService.getTeamsByEmail(email, req.headers);
  }

  @Get('/scale')
  getScale(@Req() req: Request) {
    return this.surveyService.getScale(req.headers);
  }

  @Get('/type/:type')
  getSurveyByType(@Param('type') type: string, @Req() req: Request) {
    return this.surveyService.getSurveyByType(type, req.headers);
  }

  @Get('/completed/:surveyId/user/:userId/team/:teamId')
  getSurveyCompleted(
    @Param('surveyId') surveyId: string,
    @Param('userId') userId: string,
    @Param('teamId') teamId: string,
    @Req() req: Request
  ) {
    return this.surveyService.getSurveyCompleted(surveyId, userId, teamId, req.headers);
  }

	@Get('/response/user/:userId/team/:teamId/survey/:surveyId')
	getResponseBySurvey(
    @Param('surveyId') surveyId: string,
    @Param('userId') userId: string,
    @Param('teamId') teamId: string,
		@Req() req: Request
	) {
		return this.surveyService.getResponseBySurvey(surveyId, userId, teamId, req.headers);
	}


	@Post('/response')
	postSurveyResponse(@Body() body: CreateResponseDto,  @Req() req: Request) {
		return this.surveyService.postSurveyResponse(body, req.headers);
	}
}
