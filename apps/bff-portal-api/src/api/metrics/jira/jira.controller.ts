import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Controller, Get, Param, Req } from '@nestjs/common';
import { ValidateDatePipe } from '@shinrai-way-of-work/core';
import { JiraService } from './jira.service';

@Controller('metrics-jira')
@Authenticated()
export class JiraController {
  constructor(private readonly jiraService: JiraService) {}

  @Get('projects')
  get(@Req() req: Request) {
    return this.jiraService.getProjects(req.headers);
  }

  @Get('projects/:projectId/boards')
  getBoardsByProject(@Param('projectId') projectId: string, @Req() req: Request) {
    return this.jiraService.getBoardsByProject(req.headers, projectId);
  }

  @Get('projects/:projectId/boards/:boardId/issues/:startDate/:endDate/type/:type')
  getIssuesByRangeDate(
    @Param('startDate', ValidateDatePipe) startDate: string,
    @Param('endDate', ValidateDatePipe) endDate: string,
    @Param('projectId') projectId: string,
    @Param('boardId') boardId: string,
    @Param('type') type: string,
    @Req() req: Request
  ) {
    return this.jiraService.getIssuesByRangeDate(
      req.headers,
      projectId,
      boardId,
      startDate,
      endDate,
      type
    );
  }
}
