import { Injectable } from '@nestjs/common';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';
import { HttpService } from '@nestjs/axios';
import secrets from 'apps/bff-portal-api/src/config/secrets';

@Injectable()
export class JiraService {
  private baseUrl = secrets.URL_METRICS_API;
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  async getProjects(headers: IncomingHttpHeaders) {
    const url = `${this.baseUrl}/jira/projects`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }

  async getBoardsByProject(headers: IncomingHttpHeaders, projectId: string) {
    const url = `${this.baseUrl}/jira/project/${projectId}/boards`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }

  async getIssuesByRangeDate(
    headers: IncomingHttpHeaders,
    projectId: string,
    boardId: string,
    startDate: string,
    endDate: string,
    type: string
  ) {
    const url = `${this.baseUrl}/jira/projects/${projectId}/boards/${boardId}/issues/${startDate}/${endDate}/type/${type}`;
    this.baseHeaders.headers = headers;
    return pipeRequest(this.httpService.get(url, this.baseHeaders));
  }
}
