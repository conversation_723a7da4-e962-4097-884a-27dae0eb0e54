import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { IChatMessage } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { tap } from 'rxjs/operators';

@Injectable()
export class ChatService {
  private logger = new Logger('ChatService');

  constructor(private readonly httpService: HttpService) {}

  streamChat(body: IChatMessage.Create, headers: Record<string, string | string[]>) {
    const config = this.getHeaders(headers);
    this.logger.log(
      `Making request to chat-api with auth: ${
        config.headers.Authorization ? 'present' : 'missing'
      }`
    );

    const request = this.httpService.post('/chat/stream', body, config);

    // Add error handling to the observable
    return pipeRequest(request).pipe(
      tap({
        next: () => this.logger.log('Successfully received response from chat-api'),
        error: (error: any) => {
          this.logger.error('Error from chat-api:', error.message);
          if (error.response) {
            this.logger.error('Response status:', error.response.status);
            this.logger.error('Response data:', error.response.data);
          }
        },
      })
    );
  }

  // Raw method for streaming - returns the full HTTP response
  streamChatRaw(body: IChatMessage.Create, headers: Record<string, string | string[]>) {
    const config = this.getHeaders(headers);
    this.logger.log(
      `Making raw request to chat-api with auth: ${
        config.headers.Authorization ? 'present' : 'missing'
      }`
    );

    return this.httpService.post('/chat/stream', body, config);
  }

  private getHeaders(headers: Record<string, string | string[]>) {
    // Extract authorization header (case insensitive)
    const authHeader =
      headers['authorization'] || headers['Authorization'] || headers.authorization;

    if (!authHeader) {
      this.logger.warn('No authorization header found in request');
    }

    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader,
      },
      responseType: 'stream' as const,
    };
  }
}
