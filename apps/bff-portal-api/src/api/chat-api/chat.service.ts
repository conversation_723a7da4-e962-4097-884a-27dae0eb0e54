import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { IChatMessage } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';

@Injectable()
export class ChatService {
  constructor(private readonly httpService: HttpService) {}

   streamChat(body: IChatMessage.Create, headers: any) {
    const options = this.getHeaders(headers);
    const observable = this.httpService.post('/chat/stream', body, options)
      .pipe(map(resp => resp.data));

    return observable.toPromise();
  }

  private getHeaders(headers: Record<string, string>) {
    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: headers['authorization'],
      },
      responseType: 'stream' as const,
    };
  }
}
