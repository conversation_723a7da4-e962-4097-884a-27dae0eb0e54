import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { IChatMessage } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';

@Injectable()
export class ChatService {
  private logger = new Logger('ChatService');

  constructor(private readonly httpService: HttpService) {}

  streamChat(body: IChatMessage.Create, headers: Record<string, string | string[]>) {
    const config = this.getHeaders(headers);
    this.logger.log(
      `Making request to chat-api with auth: ${
        config.headers.Authorization ? 'present' : 'missing'
      }`
    );
    return pipeRequest(this.httpService.post('/chat/stream', body, config));
  }

  private getHeaders(headers: Record<string, string | string[]>) {
    // Extract authorization header (case insensitive)
    const authHeader =
      headers['authorization'] || headers['Authorization'] || headers.authorization;

    if (!authHeader) {
      this.logger.warn('No authorization header found in request');
    }

    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader,
      },
      responseType: 'stream' as const,
    };
  }
}
