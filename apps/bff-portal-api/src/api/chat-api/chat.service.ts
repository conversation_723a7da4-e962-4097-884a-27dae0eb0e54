import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { IChatMessage } from '@shinrai-way-of-work/models';
import { IncomingHttpHeaders } from 'http';
import { pipeRequest } from '@shinrai-way-of-work/utils';

@Injectable()
export class ChatService {
  constructor(private readonly httpService: HttpService) {}

  streamChat(body: IChatMessage.Create, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.post('/chat/stream', body, this.getHeaders(headers)));
  }

  private getHeaders(headers: IncomingHttpHeaders) {
    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: headers['authorization'],
      },
    };
  }
}
