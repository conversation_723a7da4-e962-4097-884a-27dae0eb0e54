import { Body, Controller, Post, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { ChatService } from './chat.service';
import { ChatStreamRequestSchema, IChatMessage } from '@shinrai-way-of-work/models';
import { ZodValidationPipe } from 'nestjs-zod';

@Authenticated()
@Controller('chat')
export class ChatController {
  constructor(private readonly service: ChatService) {}

  @Post('stream')
  async streamChat(
    @Req() req: Request,
    @Body(new ZodValidationPipe(ChatStreamRequestSchema)) body: IChatMessage.Create
  ) {
    return this.service.streamChat(body, req.headers);
  }
}
