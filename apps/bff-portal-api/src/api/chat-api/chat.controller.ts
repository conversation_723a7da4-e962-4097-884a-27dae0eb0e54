import { Body, Controller, Post, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { ChatService } from './chat.service';
import { ChatStreamRequestSchema, IChatMessage } from '@shinrai-way-of-work/models';
import { ZodValidationPipe } from 'nestjs-zod';
import { firstValueFrom } from 'rxjs';

@Authenticated()
@Controller('chat')
export class ChatController {
  constructor(private readonly service: ChatService) {}

  @Post('stream')
  async streamChat(
    @Req() req: Request,
    @Body(new ZodValidationPipe(ChatStreamRequestSchema)) body: IChatMessage.Create,
    @Res() res: Response
  ) {
    try {
      // Set headers for Server-Sent Events
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

      // Get the streaming response from chat-api
      const response = await firstValueFrom(this.service.streamChat(body, req.headers));

      // Pipe the streaming response from chat-api to the client
      (response as any).data.pipe(res);
    } catch (error) {
      console.error('BFF Chat Controller Error:', error);
      res.status(500).json({
        error: error.message,
        details: error.response?.data || 'No additional details',
      });
    }
  }
}
