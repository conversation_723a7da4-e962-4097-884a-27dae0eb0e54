import { Body, Controller, Post, Req } from '@nestjs/common';
import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { ChatService } from './chat.service';
import { ChatStreamRequestSchema, IChatMessage } from '@shinrai-way-of-work/models';
import { ZodValidationPipe } from 'nestjs-zod';

@Authenticated()
@Controller('chat')
export class ChatController {
  constructor(private readonly service: ChatService) {}

  @Post('stream')
  async streamChat(
    @Req() req: Request,
    @Body(new ZodValidationPipe(ChatStreamRequestSchema)) body: IChatMessage.Create
  ) {
    // Convert headers to plain object
    const headers: Record<string, string> = {};
    Object.entries(req.headers).forEach(([key, value]) => {
      if (typeof value === 'string') {
        headers[key] = value;
      } else if (Array.isArray(value)) {
        headers[key] = value[0]; // Take first value if array
      }
    });

    return this.service.streamChat(body, headers);
  }
}
