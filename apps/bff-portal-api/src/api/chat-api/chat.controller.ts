import { Body, Controller, Post, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { ChatService } from './chat.service';
import { ChatStreamRequestSchema, IChatMessage } from '@shinrai-way-of-work/models';
import { ZodValidationPipe } from 'nestjs-zod';

@Authenticated()
@Controller('chat')
export class ChatController {
  constructor(private readonly service: ChatService) {}

  @Post('stream')
  streamChat(
    @Req() req: Request,
    @Body(new ZodValidationPipe(ChatStreamRequestSchema)) body: IChatMessage.Create,
    @Res({ passthrough: false }) res: Response
  ) {
    // Convert headers to the expected format
    const headers: Record<string, string | string[]> = {};
    Object.entries(req.headers).forEach(([key, value]) => {
      if (value !== undefined) {
        headers[key] = value;
      }
    });

    // Make the request to chat-api and pipe the response directly
    this.service.streamChatRaw(body, headers).subscribe({
      next: (response) => {
        // Copy headers from chat-api response
        Object.entries(response.headers).forEach(([key, value]) => {
          if (typeof value === 'string') {
            res.setHeader(key, value);
          }
        });

        // Pipe the streaming data
        response.data.pipe(res);
      },
      error: (error) => {
        console.error('BFF Chat Controller Error:', error);
        if (!res.headersSent) {
          res.status(500).json({
            error: error.message,
            details: error.response?.data || 'No additional details',
          });
        }
      },
    });
  }
}
