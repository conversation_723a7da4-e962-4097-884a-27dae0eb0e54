import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { CapabilityDto } from '@shinrai-way-of-work/models';
import { CapabilityService } from './capability.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

@Controller('capabilities')
@Authenticated()
@ApiTags('Capabilities')
@ApiBearerAuth()
export class CapabilityController {
  constructor(private readonly capabilityService: CapabilityService) {}

  @Post()
  @ApiOperation({ summary: 'Crear una nueva capability' })
  @ApiBody({ description: 'Datos de la capability a crear' })
  @ApiResponse({ status: 201, description: 'Capability creada exitosamente.' })
  @ApiResponse({ status: 400, description: 'Datos inválidos.' })
  async createCapability(
    @Body(new ZodValidationPipe(CapabilityDto.CreateSchema))
    body: CapabilityDto.CreateCapabilityDto,
    @Req() req: Request
  ) {
    return this.capabilityService.createCapability(body, req.headers);
  }

  @Post('bulk')
  @ApiOperation({ summary: 'Crear múltiples capabilities' })
  @ApiBody({ description: 'Array de capabilities a crear' })
  @ApiResponse({ status: 201, description: 'Capabilities creadas exitosamente.' })
  @ApiResponse({ status: 400, description: 'Datos inválidos.' })
  async createBulkCapabilities(
    @Body(new ZodValidationPipe(CapabilityDto.CreateBulkSchema))
    body: CapabilityDto.CreateBulkCapabilityDto,
    @Req() req: Request
  ) {
    return this.capabilityService.createBulkCapabilities(body, req.headers);
  }

  @Get()
  @ApiOperation({ summary: 'Obtener todas las capabilities' })
  @ApiQuery({ name: 'limit', required: false, description: 'Límite de resultados' })
  @ApiQuery({ name: 'offset', required: false, description: 'Desplazamiento para paginación' })
  @ApiQuery({ name: 'funcionalidad', required: false, description: 'Filtro por funcionalidad' })
  @ApiQuery({ name: 'definicion', required: false, description: 'Filtro por definición' })
  @ApiQuery({ name: 'dominio', required: false, description: 'Filtro por dominio' })
  @ApiQuery({ name: 'capacidad', required: false, description: 'Filtro por capacidad' })
  @ApiQuery({ name: 'sub_capacidad', required: false, description: 'Filtro por sub capacidad' })
  @ApiQuery({ name: 'foco_operativo', required: false, description: 'Filtro por foco operativo' })
  @ApiQuery({ name: 'cadena', required: false, description: 'Filtro por cadena' })
  @ApiQuery({ name: 'proceso', required: false, description: 'Filtro por proceso' })
  @ApiQuery({ name: 'responsable', required: false, description: 'Filtro por responsable' })
  @ApiQuery({ name: 'is_active', required: false, description: 'Filtro por estado activo' })
  @ApiQuery({ name: 'id', required: false, description: 'Filtro por ID' })
  @ApiResponse({ status: 200, description: 'Capabilities obtenidas exitosamente.' })
  async getCapabilities(@Query() query: any, @Req() req: Request) {
    return this.capabilityService.getCapabilities(query, req.headers);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener una capability por ID' })
  @ApiParam({ name: 'id', type: String, description: 'ID de la capability' })
  @ApiResponse({ status: 200, description: 'Capability obtenida exitosamente.' })
  @ApiResponse({ status: 404, description: 'Capability no encontrada.' })
  async getCapabilityById(@Param('id') id: string, @Req() req: Request) {
    return this.capabilityService.getCapabilityById(id, req.headers);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Actualizar una capability por ID' })
  @ApiParam({ name: 'id', type: String, description: 'ID de la capability' })
  @ApiBody({ description: 'Datos de la capability a actualizar' })
  @ApiResponse({ status: 200, description: 'Capability actualizada exitosamente.' })
  @ApiResponse({ status: 404, description: 'Capability no encontrada.' })
  async updateCapability(
    @Param('id') id: string,
    @Body(new ZodValidationPipe(CapabilityDto.UpdateSchema))
    body: CapabilityDto.UpdateCapabilityDto,
    @Req() req: Request
  ) {
    return this.capabilityService.updateCapability(id, body, req.headers);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Eliminar una capability por ID' })
  @ApiParam({ name: 'id', type: String, description: 'ID de la capability' })
  @ApiResponse({ status: 200, description: 'Capability eliminada exitosamente.' })
  @ApiResponse({ status: 404, description: 'Capability no encontrada.' })
  async deleteCapability(@Param('id') id: string, @Req() req: Request) {
    return this.capabilityService.deleteCapability(id, req.headers);
  }
} 