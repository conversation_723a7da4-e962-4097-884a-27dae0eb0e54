import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CapabilityController } from './capability.controller';
import { CapabilityService } from './capability.service';
import secrets from './../../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_CAPABILITIES_API}/capabilities` })],
  controllers: [CapabilityController],
  providers: [CapabilityService],
})
export class CapabilityModule {} 