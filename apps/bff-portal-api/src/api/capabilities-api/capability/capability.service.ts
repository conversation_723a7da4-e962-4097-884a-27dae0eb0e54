import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { CapabilityDto } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class CapabilityService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  createCapability(body: CapabilityDto.CreateCapabilityDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.post('', body, this.baseHeaders));
  }

  createBulkCapabilities(body: CapabilityDto.CreateBulkCapabilityDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };

    return pipeRequest(this.httpService.post('/bulk', body, this.baseHeaders));
  }

  getCapabilities(query: any, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(
      this.httpService.get('', { params: query, headers: this.baseHeaders.headers })
    );
  }

  getCapabilityById(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.get(`/${id}`, { headers: this.baseHeaders.headers }));
  }

  updateCapability(id: string, body: CapabilityDto.UpdateCapabilityDto, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.patch(`/${id}`, body, this.baseHeaders));
  }

  deleteCapability(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      Authorization: headers['authorization'],
    };
    return pipeRequest(this.httpService.delete(`/${id}`, { headers: this.baseHeaders.headers }));
  }
} 