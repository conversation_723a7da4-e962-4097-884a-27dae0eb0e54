import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { HolidayService } from './holiday.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';
import {
  HolidayCreateDto,
  IHoliday,
  HolidayUpdateDto,
  WorkingDayQueryDto,
} from '@shinrai-way-of-work/models';

@Authenticated()
@Controller('teamwork/holiday')
export class HolidayController {
  constructor(private readonly service: HolidayService) {}

  @Get('')
  async find(@Req() req: Request) {
    return this.service.find(req.headers);
  }

  @Get('/country/:id')
  async findByCountry(@Req() req: Request, @Param('id') id: string) {
    return this.service.findByCountry(id, req.headers);
  }

  @Get('/working-days')
  async getWorkingDaysByCountry(@Req() req: Request, @Query() query: WorkingDayQueryDto) {
    return this.service.getWorkingDaysByYear(query, req.headers);
  }

  @Post('')
  async create(
    @Req() req: Request,
    @Body(new ZodValidationPipe(HolidayCreateDto)) body: IHoliday.Create
  ) {
    return this.service.create(body, req.headers);
  }

  @Patch(':id')
  async update(
    @Req() req: Request,
    @Body(new ZodValidationPipe(HolidayUpdateDto)) body: IHoliday.Update,
    @Param('id') id: string
  ) {
    return this.service.update(id, body, req.headers);
  }

  @Delete(':id')
  async delete(@Req() req: Request, @Param('id') id: string) {
    return this.service.delete(id, req.headers);
  }
}
