import { HttpModule } from '@nestjs/axios';
import { HolidayController } from './holiday.controller';
import { HolidayService } from './holiday.service';
import secrets from '../../../config/secrets';
import { Module } from '@nestjs/common';

@Module({
  controllers: [HolidayController],
  providers: [HolidayService],
  imports: [HttpModule.register({ baseURL: secrets.URL_TEAMWORK_API })],
})
export class HolidayTeamWorkModule {}
