import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { IHoliday, WorkingDayQueryDto } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class HolidayService {
  constructor(private readonly httpService: HttpService) {}

  find(headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.get('/holiday', this.getHeaders(headers)));
  }

  findByCountry(id: string, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.get(`/holiday/country/${id}`, this.getHeaders(headers)));
  }

  getWorkingDaysByYear(dto: WorkingDayQueryDto, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.get('/holiday/working-days', { params: dto, ...this.getHeaders(headers) })
    );
  }

  create(body: IHoliday.Create, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.post('/holiday', body, this.getHeaders(headers)));
  }

  update(id: string, body: IHoliday.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.patch(`/holiday/${id}`, body, this.getHeaders(headers)));
  }

  delete(id: string, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.delete(`/holiday/${id}`, this.getHeaders(headers)));
  }

  private getHeaders(headers: IncomingHttpHeaders) {
    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: headers['authorization'],
      },
    };
  }
}
