import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';
import { EntryRequestQueryDto, IEntryRequest } from '@shinrai-way-of-work/models';

@Injectable()
export class EntryRequestService {
  constructor(private readonly httpService: HttpService) {}

  findAll(headers: IncomingHttpHeaders, query: EntryRequestQueryDto) {
    return pipeRequest(
      this.httpService.get('/entry-request', {
        params: query,
        headers: this.getHeaders(headers).headers,
      })
    );
  }

  create(body: IEntryRequest.Create, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.post('/entry-request', body, this.getHeaders(headers)));
  }

  forwardForCorrection(id: string, body: IEntryRequest.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/entry-request/${id}/forward`, body, this.getHeaders(headers))
    );
  }

  resendForApproval(id: string, body: IEntryRequest.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/entry-request/${id}/resend`, body, this.getHeaders(headers))
    );
  }

  accept(id: string, body: IEntryRequest.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/entry-request/${id}/accept`, body, this.getHeaders(headers))
    );
  }

  approve(id: string, body: IEntryRequest.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/entry-request/${id}/approve`, body, this.getHeaders(headers))
    );
  }

  reject(id: string, body: IEntryRequest.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/entry-request/${id}/reject`, body, this.getHeaders(headers))
    );
  }

  update(id: string, body: IEntryRequest.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/entry-request/${id}`, body, this.getHeaders(headers))
    );
  }

  updateById(id: string, body: IEntryRequest.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/entry-request/${id}`, body, this.getHeaders(headers))
    );
  }

  delete(id: string, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.delete(`/entry-request/${id}`, this.getHeaders(headers)));
  }

  private getHeaders(headers: IncomingHttpHeaders) {
    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: headers['authorization'],
      },
    };
  }
}
