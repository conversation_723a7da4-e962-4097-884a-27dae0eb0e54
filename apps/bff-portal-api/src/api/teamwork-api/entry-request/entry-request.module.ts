import { HttpModule } from '@nestjs/axios';
import secrets from '../../../config/secrets';
import { Module } from '@nestjs/common';
import { EntryRequestController } from './entry-request.controller';
import { EntryRequestService } from './entry-request.service';

@Module({
  controllers: [EntryRequestController],
  providers: [EntryRequestService],
  imports: [HttpModule.register({ baseURL: secrets.URL_TEAMWORK_API })],
})
export class EntryRequestModule {}
