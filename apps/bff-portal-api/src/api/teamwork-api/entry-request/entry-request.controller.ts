import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';
import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query, Req } from '@nestjs/common';

import { EntryRequestService } from './entry-request.service';
import {
  IEntryRequest,
  EntryRequestCreateDto,
  EntryRequestUpdateDto,
  EntryRequestQueryDto,
  StatusCommentDto,
} from '@shinrai-way-of-work/models';

@Authenticated()
@Controller('teamwork/entry-request')
export class EntryRequestController {
  constructor(private readonly service: EntryRequestService) {}

  @Get('')
  async find(@Query() query: EntryRequestQueryDto, @Req() req: Request) {
    return this.service.findAll(req.headers, query);
  }

  @Post('')
  async create(
    @Req() req: Request,
    @Body(new ZodValidationPipe(EntryRequestCreateDto)) body: IEntryRequest.Create
  ) {
    return this.service.create(body, req.headers);
  }

  @Patch(':id/forward')
  async forward(
    @Req() req: Request,
    @Body(new ZodValidationPipe(StatusCommentDto)) body: IEntryRequest.Update,
    @Param('id') id: string
  ) {
    return this.service.forwardForCorrection(id, body, req.headers);
  }

  @Patch(':id/resend')
  async resend(
    @Req() req: Request,
    @Body(new ZodValidationPipe(StatusCommentDto)) body: IEntryRequest.Update,
    @Param('id') id: string
  ) {
    return this.service.resendForApproval(id, body, req.headers);
  }

  @Patch(':id/accept')
  async accept(
    @Req() req: Request,
    @Body(new ZodValidationPipe(StatusCommentDto)) body: IEntryRequest.Update,
    @Param('id') id: string
  ) {
    return this.service.accept(id, body, req.headers);
  }

  @Patch(':id/approve')
  async approve(
    @Req() req: Request,
    @Body(new ZodValidationPipe(StatusCommentDto)) body: IEntryRequest.Update,
    @Param('id') id: string
  ) {
    return this.service.approve(id, body, req.headers);
  }

  @Patch(':id/reject')
  async reject(
    @Req() req: Request,
    @Body(new ZodValidationPipe(StatusCommentDto)) body: IEntryRequest.Update,
    @Param('id') id: string
  ) {
    return this.service.reject(id, body, req.headers);
  }

  @Patch(':id')
  async update(
    @Req() req: Request,
    @Body(new ZodValidationPipe(EntryRequestUpdateDto)) body: IEntryRequest.Update,
    @Param('id') id: string
  ) {
    return this.service.update(id, body, req.headers);
  }

  @Put(':id')
  async updateById(
    @Req() req: Request,
    @Body(new ZodValidationPipe(EntryRequestUpdateDto)) body: IEntryRequest.Update,
    @Param('id') id: string
  ) {
    return this.service.updateById(id, body, req.headers);
  }

  @Delete(':id')
  async delete(@Req() req: Request, @Param('id') id: string) {
    return this.service.delete(id, req.headers);
  }
}
