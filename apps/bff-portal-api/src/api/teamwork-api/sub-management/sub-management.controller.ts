import { Request } from 'express';
import { Authenticated } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { Body, Controller, Delete, Get, Param, Patch, Post, Req } from '@nestjs/common';
import { SubManagementService } from './sub-management.service';
import { ZodValidationPipe } from '@shinrai-way-of-work/core';
import {
  ISubManagement,
  SubManagementCreateDto,
  SubManagementUpdateDto,
} from '@shinrai-way-of-work/models';

@Authenticated()
@Controller('teamwork/sub-management')
export class SubManagementTeamWorkController {
  constructor(private readonly service: SubManagementService) {}

  @Get('')
  async find(@Req() req: Request) {
    return this.service.find(req.headers);
  }

  @Post('')
  async create(
    @Req() req: Request,
    @Body(new ZodValidationPipe(SubManagementCreateDto)) body: ISubManagement.Create
  ) {
    return this.service.create(body, req.headers);
  }

  @Patch(':id')
  async update(
    @Req() req: Request,
    @Body(new ZodValidationPipe(SubManagementUpdateDto)) body: ISubManagement.Update,
    @Param('id') id: string
  ) {
    return this.service.update(id, body, req.headers);
  }

  @Delete(':id')
  async delete(@Req() req: Request, @Param('id') id: string) {
    return this.service.delete(id, req.headers);
  }
}
