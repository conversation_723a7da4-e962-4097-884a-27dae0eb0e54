import { HttpModule } from '@nestjs/axios';
import { SubManagementTeamWorkController } from './sub-management.controller';
import { SubManagementService } from './sub-management.service';
import secrets from '../../../config/secrets';
import { Module } from '@nestjs/common';

@Module({
  controllers: [SubManagementTeamWorkController],
  providers: [SubManagementService],
  imports: [HttpModule.register({ baseURL: secrets.URL_TEAMWORK_API })],
})
export class SubManagementTeamWorkModule {}
