import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ISubManagement } from '@shinrai-way-of-work/models';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class SubManagementService {
  constructor(private readonly httpService: HttpService) {}

  find(headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.get('/sub-management', this.getHeaders(headers)));
  }

  create(body: ISubManagement.Create, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.post('/sub-management', body, this.getHeaders(headers)));
  }

  update(id: string, body: ISubManagement.Update, headers: IncomingHttpHeaders) {
    return pipeRequest(
      this.httpService.patch(`/sub-management/${id}`, body, this.getHeaders(headers))
    );
  }

  delete(id: string, headers: IncomingHttpHeaders) {
    return pipeRequest(this.httpService.delete(`/sub-management/${id}`, this.getHeaders(headers)));
  }

  private getHeaders(headers: IncomingHttpHeaders) {
    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: headers['authorization'],
      },
    };
  }
}
