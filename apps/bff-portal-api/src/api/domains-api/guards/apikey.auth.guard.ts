import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from "@nestjs/common";
import { NestJS } from '@team_seki/winston-logger'
import secrets from "apps/bff-portal-api/src/config/secrets";

@Injectable()
export class Api<PERSON>eyAuthGuard implements CanActivate {
  constructor (private logger: NestJS.NestLogger) {}

  canActivate = async (context: ExecutionContext): Promise<boolean> => {
    try {
      const request = context.switchToHttp().getRequest()

      const key = request.headers['pmo-apikey'] ?? ''

      return key === secrets?.PMO_API_KEY

    } catch (error) {
      const request = context.switchToHttp().getRequest()
      this.logger.error('API KEY Authentication error', request)
      throw new UnauthorizedException()
    }
  }
}
