import { Controller, Get, Req, UseGuards } from "@nestjs/common";
import { DomainService } from "./domain.service";
import { Request } from 'express'
import { Public } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { ApiKeyAuthGuard } from "../guards/apikey.auth.guard";

@Controller('domains')
@Public()
@UseGuards(ApiKeyAuthGuard)
export class DomainController {
  constructor (private readonly domainService: DomainService) {}

  @Get()
  async getDomains(@Req() req: Request) {
    return this.domainService.getDomains(req.headers)
  }
}
