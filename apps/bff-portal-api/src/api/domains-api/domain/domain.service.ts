import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class DomainService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  getDomains(headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      'pmo-apikey': headers['pmo-apikey'],
    };

    return pipeRequest(this.httpService.get('/domains', this.baseHeaders));
  }
}
