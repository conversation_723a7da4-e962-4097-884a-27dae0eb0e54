import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { DomainController } from "./domain.controller";
import { DomainService } from "./domain.service";
import secrets from "apps/bff-portal-api/src/config/secrets";

@Module({
  imports: [HttpModule.register({
    baseURL: `${secrets.URL_BUSINESS_CASE_API}/pmo`
  })],
  controllers: [DomainController],
  providers: [DomainService]
})
export class DomainModule {}
