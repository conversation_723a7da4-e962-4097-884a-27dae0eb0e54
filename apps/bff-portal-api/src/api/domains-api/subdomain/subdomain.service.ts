import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { pipeRequest } from '@shinrai-way-of-work/utils';
import { IncomingHttpHeaders } from 'http';

@Injectable()
export class SubdomainService {
  private baseHeaders = {
    headers: {},
  };

  constructor(private readonly httpService: HttpService) {}

  getSubdomains(id: string, headers: IncomingHttpHeaders) {
    this.baseHeaders.headers = {
      'Content-Type': 'application/json',
      'pmo-apikey': headers['pmo-apikey'],
    };

    return pipeRequest(this.httpService.get(`/subdomains/${id}`, this.baseHeaders));
  }
}
