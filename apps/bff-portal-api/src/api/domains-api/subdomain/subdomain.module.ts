import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { SubdomainController } from './subdomain.controller';
import { SubdomainService } from './subdomain.service';
import secrets from 'apps/bff-portal-api/src/config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: `${secrets.URL_BUSINESS_CASE_API}/pmo` })],
  controllers: [SubdomainController],
  providers: [SubdomainService],
})
export class SubdomainModule {}
