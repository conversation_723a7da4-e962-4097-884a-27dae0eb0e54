import { Controller, Get, Param, Req, UseGuards } from "@nestjs/common";
import { SubdomainService } from "./subdomain.service";
import { Request } from 'express'
import { Public } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { ApiKeyAuthGuard } from "../guards/apikey.auth.guard";

@Controller('subdomains')
@Public()
@UseGuards(ApiKeyAuthGuard)
export class SubdomainController {
  constructor (private readonly subdomainService: SubdomainService) {}

  @Get(':id')
  async getSubdomains(@Param('id') id: string, @Req() req: Request) {
    return this.subdomainService.getSubdomains(id, req.headers)
  }
}
