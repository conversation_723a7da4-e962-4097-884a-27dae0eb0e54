import * as path from 'path';
import { Module } from '@nestjs/common';
import { HealthModule } from './health/health.module';
import { MetricsModule } from './metrics/metrics.module';
import { JwtModule } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
//import { ProductsApiModule } from './products-api/products.module';
import { SurveyModule } from './metrics/survey-culture/survey.module';
import { UsersModule } from './users/module';
import { FileModule } from './file-manager/file.module';
import { HeroNewsModule } from './hero-news-api/hero-news.module';
import { LogLevel, NestJS } from '@team_seki/winston-logger';
import secrets from '../config/secrets';
import { QuestionsModule } from './questions-api/questions.module';
import { InitiativesModule } from './initiative-api/initiatives.module';
import { DomainsModule } from './domains-api/domains.module';
import { MetricsModule as NewMetricsModule } from './metrics-api/metrics.module';
import { TeamWorkModule } from './teamwork-api/teamwork.module';
import { ActiveDirectoryModule } from './active-directory/module';
import { CapabilityModule } from './capabilities-api/capability/capability.module';

@Module({
  imports: [
    NestJS.LoggerModule.forRoot({
      appName: secrets.PRODUCT_NAME,
      environment: process.env.NODE_ENV,
      level: secrets.LOG_LEVEL as LogLevel,
      outputFormat: process.env.NODE_ENV == 'development' ? 'text' : 'json',
    }),
    HealthModule,
    DomainsModule,
    NewMetricsModule,
    MetricsModule,
    //ProductsApiModule,
    ActiveDirectoryModule,
    SurveyModule,
    UsersModule,
    ActiveDirectoryModule,
    FileModule,
    HeroNewsModule,
    QuestionsModule,
    InitiativesModule,
    TeamWorkModule,
    CapabilityModule,
    JwtModule.forRoot({
      allowedIssuer: 'shinrai',
      //privateKeyPath: path.join(__dirname, 'config', 'private_key.pem'),
      publicKeyPath: path.join(__dirname, 'config', 'public_key.pub'),
      expiration_in_minutes: 60,
      algorithm: 'RS512',
    }),
  ],
})
export class ApiModule {}
