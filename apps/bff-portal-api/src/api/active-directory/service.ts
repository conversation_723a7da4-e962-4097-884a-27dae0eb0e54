import { Injectable } from '@nestjs/common';
import axios from 'axios';
import secrets from '../../config/secrets';

@Injectable()
export class ActiveDirectoryServices {

  async findAll(search: string): Promise<{ [key: string]: string }> {
    const response = await axios.get(`${secrets.AD_URL_API}/find_all/${search}`,{
      headers:{
        'apiKey': secrets.AD_APIKEY
      }
    })
    return response.data.users
  }
}

export default ActiveDirectoryServices;
