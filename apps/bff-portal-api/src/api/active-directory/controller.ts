import { Controller, Get, Param } from '@nestjs/common';
import * as SDK from '@team_yumi/node-sdk';
import { ActiveDirectoryServices } from './service';

@Controller('active-directory')
@SDK.Lib.Nest.Modules.Jwt.Authenticated()
export class ActiveDirectoryController {
  constructor(private readonly service: ActiveDirectoryServices) { }

  @Get("/find_all/:search")
  findAll(@Param('search') search: string) {
    return this.service.findAll(search);
  }
}

export default ActiveDirectoryController;
