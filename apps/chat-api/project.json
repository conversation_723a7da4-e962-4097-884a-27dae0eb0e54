{"name": "chat-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/chat-api/src", "projectType": "application", "targets": {"build": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "release", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/chat-api", "main": "apps/chat-api/src/main.ts", "tsConfig": "apps/chat-api/tsconfig.app.json", "assets": ["apps/chat-api/src/config"], "webpackConfig": "apps/chat-api/webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "inspect": true}, "release": {"optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": true, "inspect": false}}}, "serve": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "chat-api:build"}, "configurations": {"development": {"buildTarget": "chat-api:build:development"}, "release": {"buildTarget": "chat-api:build:release", "hmr": false}}}, "lint": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/chat-api/**/*.ts"]}}, "test": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/chat-api"], "options": {"jestConfig": "apps/chat-api/jest.config.ts", "passWithNoTests": true}}, "secrets": {"executor": "nx:run-commands", "options": {"command": "seki secrets generate -p=chat-api", "cwd": "."}}}, "tags": ["REQUIRED:<PERSON>LDEN"]}