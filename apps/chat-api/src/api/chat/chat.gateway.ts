import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  WebSocketServer,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { ChatService } from './chat.service';
import { CreateChatMessageDto } from '@shinrai-way-of-work/models';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/chat',
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private logger: Logger = new Logger('ChatGateway');

  constructor(private readonly chatService: ChatService) {}

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    client.emit('connection', { message: 'Connected to chat server' });
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('sendMessage')
  async handleMessage(
    @MessageBody() createChatMessageDto: CreateChatMessageDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      // Create the user message
      const userMessage = await this.chatService.createMessage(createChatMessageDto);
      
      // Emit the user message to all clients
      this.server.emit('messageReceived', userMessage);

      // If it's a user message, simulate an assistant response
      if (createChatMessageDto.role === 'user') {
        await this.simulateAssistantResponse(createChatMessageDto.content, client);
      }

      return userMessage;
    } catch (error) {
      this.logger.error('Error handling message:', error);
      client.emit('error', { message: 'Failed to process message' });
    }
  }

  @SubscribeMessage('updateMessage')
  async handleUpdateMessage(
    @MessageBody() data: { id: string; isStreaming?: boolean; error?: string; content?: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const updatedMessage = await this.chatService.update(data.id, {
        isStreaming: data.isStreaming,
        error: data.error,
        content: data.content,
      });

      // Emit the updated message to all clients
      this.server.emit('messageUpdated', updatedMessage);

      return updatedMessage;
    } catch (error) {
      this.logger.error('Error updating message:', error);
      client.emit('error', { message: 'Failed to update message' });
    }
  }

  @SubscribeMessage('getStreamingMessages')
  async handleGetStreamingMessages(@ConnectedSocket() client: Socket) {
    try {
      const streamingMessages = await this.chatService.findStreamingMessages();
      client.emit('streamingMessages', streamingMessages);
      return streamingMessages;
    } catch (error) {
      this.logger.error('Error getting streaming messages:', error);
      client.emit('error', { message: 'Failed to get streaming messages' });
    }
  }

  private async simulateAssistantResponse(userContent: string, client: Socket) {
    // Create an assistant message with streaming enabled
    const assistantMessage = await this.chatService.createMessage({
      role: 'assistant',
      content: '',
      isStreaming: true,
    });

    // Emit the initial streaming message
    this.server.emit('messageReceived', assistantMessage);

    // Simulate streaming response
    const responseText = `I understand you said: "${userContent}". Let me help you with that.`;
    const words = responseText.split(' ');
    
    let currentContent = '';
    
    for (let i = 0; i < words.length; i++) {
      currentContent += (i > 0 ? ' ' : '') + words[i];
      
      // Update the message content
      const updatedMessage = await this.chatService.update(assistantMessage.id, {
        content: currentContent,
        isStreaming: i < words.length - 1, // Stop streaming on last word
      });

      // Emit the updated streaming message
      this.server.emit('messageUpdated', updatedMessage);

      // Simulate delay between words
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  // Method to emit message updates from external services
  async emitMessageUpdate(messageId: string, update: any) {
    const updatedMessage = await this.chatService.update(messageId, update);
    this.server.emit('messageUpdated', updatedMessage);
    return updatedMessage;
  }

  // Method to emit new messages from external services
  async emitNewMessage(createChatMessageDto: CreateChatMessageDto) {
    const message = await this.chatService.createMessage(createChatMessageDto);
    this.server.emit('messageReceived', message);
    return message;
  }
}
