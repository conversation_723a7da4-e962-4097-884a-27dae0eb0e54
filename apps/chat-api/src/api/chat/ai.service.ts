import { Injectable, Logger } from '@nestjs/common';
import { anthropic } from '@ai-sdk/anthropic';
import { streamText, generateText, CoreMessage } from 'ai';
import secrets from '../../config/secrets';

@Injectable()
export class AIService {
  private logger: Logger = new Logger('AIService');
  private model = anthropic('claude-3-sonnet-20240229');

  constructor() {
    if (!secrets.ANTHROPIC_API_KEY || secrets.ANTHROPIC_API_KEY === 'your-anthropic-api-key-here') {
      this.logger.warn('ANTHROPIC_API_KEY not configured properly');
    }
  }

  /**
   * Generates a streaming response from Claude
   */
  async generateStreamingResponse(messages: CoreMessage[]) {
    try {
      const result = await streamText({
        model: this.model,
        messages,
        apiKey: secrets.ANTHROPIC_API_KEY,
        maxTokens: 1000,
        temperature: 0.7,
      });

      return result;
    } catch (error) {
      this.logger.error('Error generating streaming response:', error);
      throw error;
    }
  }

  /**
   * Generates a non-streaming response from Claude
   */
  async generateResponse(messages: CoreMessage[]) {
    try {
      const result = await generateText({
        model: this.model,
        messages,
        apiKey: secrets.ANTHROPIC_API_KEY,
        maxTokens: 1000,
        temperature: 0.7,
      });

      return result;
    } catch (error) {
      this.logger.error('Error generating response:', error);
      throw error;
    }
  }

  /**
   * Converts ChatMessage format to CoreMessage format
   */
  convertToCoreMessages(messages: Array<{ role: 'user' | 'assistant'; content: string }>): CoreMessage[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
    }));
  }

  /**
   * Validates that messages array is properly formatted
   */
  validateMessages(messages: Array<{ role: string; content: string }>): boolean {
    if (!Array.isArray(messages) || messages.length === 0) {
      return false;
    }

    return messages.every(msg => 
      msg && 
      typeof msg.role === 'string' && 
      (msg.role === 'user' || msg.role === 'assistant') &&
      typeof msg.content === 'string' &&
      msg.content.trim().length > 0
    );
  }
}
