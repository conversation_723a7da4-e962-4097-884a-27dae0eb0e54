import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ChatService } from './chat.service';
import { AIService } from './ai.service';
import { ChatStreamRequest } from '@shinrai-way-of-work/models';

@Injectable()
export class ChatStreamingService {
  private logger: Logger = new Logger('ChatStreamingService');

  constructor(private readonly chatService: ChatService, private readonly aiService: AIService) {}

  /**
   * Generates a streaming response from <PERSON> based on conversation messages
   */
  async *generateClaudeStream(
    streamRequest: ChatStreamRequest,
    userInfo?: any
  ): AsyncGenerator<any, void, unknown> {
    // Validate messages
    if (!this.aiService.validateMessages(streamRequest.messages)) {
      throw new BadRequestException('Invalid messages format');
    }

    // Save user messages to database
    const savedMessages = [];
    for (const msg of streamRequest.messages) {
      if (msg.role === 'user') {
        const savedMessage = await this.chatService.createMessage(
          {
            role: msg.role,
            content: msg.content,
            isStreaming: false,
          },
          userInfo
        );
        savedMessages.push(savedMessage);
      }
    }

    // Create initial assistant message with streaming enabled
    const assistantMessage = await this.chatService.createMessage(
      {
        role: 'assistant',
        content: '',
        isStreaming: true,
      },
      userInfo
    );

    yield {
      type: 'message_start',
      message: assistantMessage,
    };

    try {
      // Convert to CoreMessage format for AI SDK
      const coreMessages = this.aiService.convertToCoreMessages(streamRequest.messages);

      // Get streaming response from Claude
      const streamResult = await this.aiService.generateStreamingResponse(coreMessages);

      let fullContent = '';

      // Stream the response
      for await (const delta of streamResult.textStream) {
        fullContent += delta;

        // Update the message in database
        const updatedMessage = await this.chatService.update(assistantMessage.id, {
          content: fullContent,
          isStreaming: true,
        });

        yield {
          type: 'content_delta',
          delta: delta,
          message: updatedMessage,
        };
      }

      // Complete the streaming
      const finalMessage = await this.chatService.update(assistantMessage.id, {
        content: fullContent,
        isStreaming: false,
      });

      yield {
        type: 'message_complete',
        message: finalMessage,
      };
    } catch (error) {
      this.logger.error('Error in Claude streaming:', error);

      // Update message with error
      const errorMessage = await this.chatService.update(assistantMessage.id, {
        isStreaming: false,
        error: error.message || 'Error generating response',
      });

      yield {
        type: 'error',
        error: error.message || 'Error generating response',
        message: errorMessage,
      };
    }
  }

  /**
   * Creates a Server-Sent Events stream for HTTP streaming
   */
  async *createSSEStream(
    streamRequest: ChatStreamRequest,
    userInfo?: any
  ): AsyncGenerator<string, void, unknown> {
    const messageGenerator = this.generateClaudeStream(streamRequest, userInfo);

    for await (const chunk of messageGenerator) {
      yield `data: ${JSON.stringify(chunk)}\n\n`;
    }

    yield 'data: [DONE]\n\n';
  }

  /**
   * Gets all currently streaming messages
   */
  async getStreamingMessages() {
    return await this.chatService.findStreamingMessages();
  }
}
