import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ChatService } from './chat.service';
import { AIService } from './ai.service';
import { CreateChatMessageDto, ChatStreamRequest } from '@shinrai-way-of-work/models';
import { generateMessageId } from '../../utils/chat.utils';

@Injectable()
export class ChatStreamingService {
  private logger: Logger = new Logger('ChatStreamingService');

  constructor(private readonly chatService: ChatService, private readonly aiService: AIService) {}

  /**
   * Creates a streaming message and returns a generator for streaming content
   */
  async *createStreamingMessage(
    createChatMessageDto: CreateChatMessageDto,
    onUpdate?: (message: any) => void
  ): AsyncGenerator<any, void, unknown> {
    // Create initial message with streaming enabled
    const message = await this.chatService.createMessage({
      ...createChatMessageDto,
      isStreaming: true,
      content: createChatMessageDto.content || '',
    });

    yield message;

    // If there's an onUpdate callback, call it
    if (onUpdate) {
      onUpdate(message);
    }

    // If this is an assistant message and content is empty, simulate streaming
    if (createChatMessageDto.role === 'assistant' && !createChatMessageDto.content) {
      yield* this.simulateAssistantStreaming(message.id, onUpdate);
    }
  }

  /**
   * Updates a streaming message with new content
   */
  async updateStreamingMessage(
    messageId: string,
    content: string,
    isComplete: boolean = false,
    error?: string,
    onUpdate?: (message: any) => void
  ) {
    const updatedMessage = await this.chatService.update(messageId, {
      content,
      isStreaming: !isComplete,
      error,
    });

    if (onUpdate) {
      onUpdate(updatedMessage);
    }

    return updatedMessage;
  }

  /**
   * Completes a streaming message
   */
  async completeStreamingMessage(
    messageId: string,
    finalContent?: string,
    error?: string,
    onUpdate?: (message: any) => void
  ) {
    const updatedMessage = await this.chatService.update(messageId, {
      ...(finalContent && { content: finalContent }),
      isStreaming: false,
      error,
    });

    if (onUpdate) {
      onUpdate(updatedMessage);
    }

    return updatedMessage;
  }

  /**
   * Gets all currently streaming messages
   */
  async getStreamingMessages() {
    return await this.chatService.findStreamingMessages();
  }

  /**
   * Simulates streaming for assistant responses
   */
  private async *simulateAssistantStreaming(
    messageId: string,
    onUpdate?: (message: any) => void
  ): AsyncGenerator<any, void, unknown> {
    const responses = [
      "I'm thinking about your question...",
      'Let me process that information...',
      "Here's what I can help you with:",
      'Based on your request, I suggest...',
    ];

    const selectedResponse = responses[Math.floor(Math.random() * responses.length)];
    const words = selectedResponse.split(' ');

    let currentContent = '';

    for (let i = 0; i < words.length; i++) {
      currentContent += (i > 0 ? ' ' : '') + words[i];

      const updatedMessage = await this.updateStreamingMessage(
        messageId,
        currentContent,
        i === words.length - 1, // Complete on last word
        undefined,
        onUpdate
      );

      yield updatedMessage;

      // Simulate delay between words
      if (i < words.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    }
  }

  /**
   * Handles streaming from external AI services
   */
  async handleExternalStreaming(
    messageId: string,
    streamGenerator: AsyncGenerator<string, void, unknown>,
    onUpdate?: (message: any) => void
  ) {
    let currentContent = '';

    try {
      for await (const chunk of streamGenerator) {
        currentContent += chunk;

        const updatedMessage = await this.updateStreamingMessage(
          messageId,
          currentContent,
          false, // Not complete yet
          undefined,
          onUpdate
        );

        // You can yield here if you want to make this method a generator too
      }

      // Complete the streaming
      await this.completeStreamingMessage(messageId, currentContent, undefined, onUpdate);
    } catch (error) {
      this.logger.error('Error in external streaming:', error);
      await this.completeStreamingMessage(
        messageId,
        currentContent,
        'Error occurred during streaming',
        onUpdate
      );
    }
  }

  /**
   * Creates a Server-Sent Events stream for HTTP streaming
   */
  async *createSSEStream(
    createChatMessageDto: CreateChatMessageDto
  ): AsyncGenerator<string, void, unknown> {
    const messageGenerator = this.createStreamingMessage(createChatMessageDto);

    for await (const message of messageGenerator) {
      yield `data: ${JSON.stringify(message)}\n\n`;
    }

    yield 'data: [DONE]\n\n';
  }
}
