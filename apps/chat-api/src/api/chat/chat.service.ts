import { Injectable, Logger } from '@nestjs/common';
import { createAnthropic } from '@ai-sdk/anthropic';
import { streamText, CoreMessage } from 'ai';
import secrets from '../../config/secrets';

@Injectable()
export class ChatService {
  private logger: Logger = new Logger('ChatService');
  private anthropicClient = createAnthropic({
    apiKey: secrets.ANTHROPIC_API_KEY,
  });

  constructor() {
    if (!secrets.ANTHROPIC_API_KEY || secrets.ANTHROPIC_API_KEY === 'your-anthropic-api-key-here') {
      this.logger.warn('ANTHROPIC_API_KEY not configured properly');
    }
  }

  streamChat(messages: CoreMessage[]) {
    try {
      this.logger.log('Creating streamText with Anthropic...');
      this.logger.log('Messages to send:', JSON.stringify(messages, null, 2));

      // Validate messages format
      if (!Array.isArray(messages) || messages.length === 0) {
        throw new Error('Messages must be a non-empty array');
      }

      for (const message of messages) {
        if (!message.role || !message.content) {
          throw new Error(`Invalid message format: ${JSON.stringify(message)}`);
        }
        if (!['user', 'assistant', 'system'].includes(message.role)) {
          throw new Error(
            `Invalid role: ${message.role}. Must be 'user', 'assistant', or 'system'`
          );
        }
      }

      this.logger.log('Messages validation passed');

      // Use the correct Claude model name
      const result = streamText({
        model: this.anthropicClient('claude-3-5-sonnet-20241022'),
        messages,
        maxTokens: 1000,
        temperature: 0.7,
        system: 'Eres un asistente útil que responde en español.',
      });

      this.logger.log('StreamText configuration:', {
        model: 'claude-3-5-sonnet-20241022',
        messagesCount: messages.length,
        maxTokens: 1000,
        temperature: 0.7,
      });

      this.logger.log('StreamText created successfully');
      return result;
    } catch (error) {
      this.logger.error('Error generating streaming response:', error);
      this.logger.error('Error details:', JSON.stringify(error, null, 2));
      throw error;
    }
  }
}
