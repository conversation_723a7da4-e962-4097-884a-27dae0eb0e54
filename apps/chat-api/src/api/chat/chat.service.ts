import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatMessage, ChatMessageDocument } from '@shinrai-way-of-work/models';
import { CreateChatMessageDto, UpdateChatMessageDto, ChatMessageQueryDto } from '@shinrai-way-of-work/models';
import { generateMessageId, sanitizeContent } from '../../utils/chat.utils';

@Injectable()
export class ChatService {
  constructor(
    @InjectModel(ChatMessage.name) private chatMessageModel: Model<ChatMessageDocument>,
  ) {}

  async createMessage(createChatMessageDto: CreateChatMessageDto, userInfo?: any): Promise<ChatMessage> {
    const messageId = generateMessageId();
    const sanitizedContent = sanitizeContent(createChatMessageDto.content);

    const chatMessage = new this.chatMessageModel({
      id: messageId,
      role: createChatMessageDto.role,
      content: sanitizedContent,
      isStreaming: createChatMessageDto.isStreaming || false,
      error: createChatMessageDto.error,
      created_by: userInfo ? {
        name: userInfo.name,
        email: userInfo.email,
        identifier: userInfo.identifier,
      } : undefined,
      is_active: true,
    });

    return await chatMessage.save();
  }

  async findAll(query: ChatMessageQueryDto): Promise<{ messages: ChatMessage[]; total: number }> {
    const filter: any = { is_active: true };
    
    if (query.role) {
      filter.role = query.role;
    }
    
    if (query.isStreaming !== undefined) {
      filter.isStreaming = query.isStreaming;
    }

    const total = await this.chatMessageModel.countDocuments(filter);
    const messages = await this.chatMessageModel
      .find(filter)
      .sort({ created_at: -1 })
      .skip(query.offset || 0)
      .limit(query.limit || 20)
      .exec();

    return { messages, total };
  }

  async findOne(id: string): Promise<ChatMessage> {
    const message = await this.chatMessageModel.findOne({ id, is_active: true }).exec();
    
    if (!message) {
      throw new NotFoundException(`Chat message with id ${id} not found`);
    }
    
    return message;
  }

  async update(id: string, updateChatMessageDto: UpdateChatMessageDto, userInfo?: any): Promise<ChatMessage> {
    const existingMessage = await this.findOne(id);
    
    const updateData: any = {};
    
    if (updateChatMessageDto.content !== undefined) {
      updateData.content = sanitizeContent(updateChatMessageDto.content);
    }
    
    if (updateChatMessageDto.isStreaming !== undefined) {
      updateData.isStreaming = updateChatMessageDto.isStreaming;
    }
    
    if (updateChatMessageDto.error !== undefined) {
      updateData.error = updateChatMessageDto.error;
    }
    
    if (userInfo) {
      updateData.updated_by = {
        name: userInfo.name,
        email: userInfo.email,
        identifier: userInfo.identifier,
      };
    }

    const updatedMessage = await this.chatMessageModel
      .findOneAndUpdate(
        { id, is_active: true },
        updateData,
        { new: true }
      )
      .exec();

    if (!updatedMessage) {
      throw new NotFoundException(`Chat message with id ${id} not found`);
    }

    return updatedMessage;
  }

  async remove(id: string): Promise<void> {
    const result = await this.chatMessageModel
      .findOneAndUpdate(
        { id, is_active: true },
        { is_active: false },
        { new: true }
      )
      .exec();

    if (!result) {
      throw new NotFoundException(`Chat message with id ${id} not found`);
    }
  }

  async findByRole(role: 'user' | 'assistant', limit: number = 20): Promise<ChatMessage[]> {
    return await this.chatMessageModel
      .find({ role, is_active: true })
      .sort({ created_at: -1 })
      .limit(limit)
      .exec();
  }

  async findStreamingMessages(): Promise<ChatMessage[]> {
    return await this.chatMessageModel
      .find({ isStreaming: true, is_active: true })
      .sort({ created_at: -1 })
      .exec();
  }
}
