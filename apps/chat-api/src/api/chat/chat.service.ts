import { Injectable, Logger } from '@nestjs/common';
import { createAnthropic } from '@ai-sdk/anthropic';
import { streamText, CoreMessage } from 'ai';
import secrets from '../../config/secrets';
import { CustomJwtEntity } from '@shinrai-way-of-work/models';

@Injectable()
export class ChatService {
  private logger: Logger = new Logger('ChatService');
  private anthropicClient = createAnthropic({
    apiKey: secrets.ANTHROPIC_API_KEY,
  });

  constructor() {
    if (!secrets.ANTHROPIC_API_KEY || secrets.ANTHROPIC_API_KEY === 'your-anthropic-api-key-here') {
      this.logger.warn('ANTHROPIC_API_KEY not configured properly');
    }
  }

  async streamChat(messages: CoreMessage[]) {
    try {
      const result = streamText({
        model: this.anthropicClient('claude-3-sonnet-20240229'),
        messages,
        maxTokens: 1000,
        prompt: 'Role: Eres un analista de datos',
      });

      return { data: result, statusCode: 200 };
    } catch (error) {
      this.logger.error('Error generating streaming response:', error);
      throw error;
    }
  }
}
