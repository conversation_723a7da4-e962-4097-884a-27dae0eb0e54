import { Injectable, Logger } from '@nestjs/common';
import { createAnthropic } from '@ai-sdk/anthropic';
import { streamText, CoreMessage } from 'ai';
import secrets from '../../config/secrets';

@Injectable()
export class ChatService {
  private logger: Logger = new Logger('ChatService');
  private anthropicClient = createAnthropic({
    apiKey: secrets.ANTHROPIC_API_KEY,
  });

  constructor() {
    if (!secrets.ANTHROPIC_API_KEY || secrets.ANTHROPIC_API_KEY === 'your-anthropic-api-key-here') {
      this.logger.warn('ANTHROPIC_API_KEY not configured properly');
    }
  }

  streamChat(messages: CoreMessage[]) {
    try {
      this.logger.log('Creating streamText with Anthropic...');
      this.logger.log('Messages to send:', JSON.stringify(messages, null, 2));

      // Validate messages format
      if (!Array.isArray(messages) || messages.length === 0) {
        throw new Error('Messages must be a non-empty array');
      }

      for (const message of messages) {
        if (!message.role || !message.content) {
          throw new Error(`Invalid message format: ${JSON.stringify(message)}`);
        }
        if (!['user', 'assistant', 'system'].includes(message.role)) {
          throw new Error(
            `Invalid role: ${message.role}. Must be 'user', 'assistant', or 'system'`
          );
        }
      }

      this.logger.log('Messages validation passed');

      const result = streamText({
        model: this.anthropicClient('claude-3-sonnet-20240229'),
        messages,
        maxTokens: 1000,
        system: 'Role: Eres un analista de datos',
      });

      this.logger.log('StreamText created successfully');
      return result;
    } catch (error) {
      this.logger.error('Error generating streaming response:', error);
      this.logger.error('Error details:', JSON.stringify(error, null, 2));
      throw error;
    }
  }
}
