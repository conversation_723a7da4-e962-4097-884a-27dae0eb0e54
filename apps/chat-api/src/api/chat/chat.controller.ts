import { <PERSON>, <PERSON>, Body, Res } from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiBody, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { ChatService } from './chat.service';
import { ChatStreamRequestSchema, IChatMessage } from '@shinrai-way-of-work/models';
import { createZodDto, ZodValidationPipe } from 'nestjs-zod';
import { CoreMessage } from 'ai';
import * as SDK from '@team_yumi/node-sdk';

@SDK.Lib.Nest.Modules.Jwt.Authenticated()
@ApiTags('Chat')
@Controller('chat')
@ApiBearerAuth()
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('stream')
  @ApiOperation({
    summary: 'Generate streaming response from <PERSON>',
    description:
      'Sends an array of messages to <PERSON> and returns a streaming response using Server-Sent Events.',
  })
  @ApiResponse({ status: 200, description: 'Streaming response from <PERSON>' })
  @ApiBody({ type: createZodDto(ChatStreamRequestSchema), required: true })
  streamChat(
    @Body(new ZodValidationPipe(ChatStreamRequestSchema)) data: { messages: CoreMessage[] },
    @Res({ passthrough: false }) res: Response
  ) {
    console.log('Chat API: Received request with messages:', data.messages?.length || 0);

    try {
      const result = this.chatService.streamChat(data.messages);

      res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      console.log('Chat API: Starting to stream response');

      // Use the AI SDK's built-in method to pipe to response
      result.pipeDataStreamToResponse(res);

      // IMPORTANTE: No retornar nada cuando usas @Res()
    } catch (error) {
      console.error('Chat API Error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: error.message });
      }
    }
  }
}

export default ChatController;
