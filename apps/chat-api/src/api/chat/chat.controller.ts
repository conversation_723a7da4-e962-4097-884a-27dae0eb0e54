import { <PERSON>, <PERSON>, Body, Res } from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiBody, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { ChatService } from './chat.service';
import { ChatStreamRequestSchema, IChatMessage } from '@shinrai-way-of-work/models';
import { createZodDto, ZodValidationPipe } from 'nestjs-zod';
import { CoreMessage } from 'ai';
import * as SDK from '@team_yumi/node-sdk';

@SDK.Lib.Nest.Modules.Jwt.Authenticated()
@ApiTags('Chat')
@Controller('chat')
@ApiBearerAuth()
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('stream')
  @ApiOperation({
    summary: 'Generate streaming response from <PERSON>',
    description:
      'Sends an array of messages to <PERSON> and returns a streaming response using Server-Sent Events.',
  })
  @ApiResponse({ status: 200, description: 'Streaming response from <PERSON>' })
  @ApiBody({ type: createZodDto(ChatStreamRequestSchema), required: true })
  streamChat(
    @Body(new ZodValidationPipe(ChatStreamRequestSchema)) data: { messages: CoreMessage[] },
    @Res({ passthrough: false }) res: Response
  ) {
    console.log('Chat API: Received request with messages:', data.messages?.length || 0);
    console.log('Chat API: Messages content:', JSON.stringify(data.messages, null, 2));

    try {
      const result = this.chatService.streamChat(data.messages);

      res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      console.log('Chat API: Headers set, starting to stream response');

      // Add manual streaming with error handling
      try {
        console.log('Chat API: About to start manual streaming...');

        // Try manual streaming first to see if we get data
        const textStream = result.textStream;
        const reader = textStream.getReader();

        console.log('Chat API: Got text stream reader');

        const processStream = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read();

              if (done) {
                console.log('Chat API: Stream completed');
                res.write('data: [DONE]\n\n');
                res.end();
                break;
              }

              console.log('Chat API: Received chunk:', value);
              res.write(`data: ${JSON.stringify({ content: value })}\n\n`);
            }
          } catch (streamError) {
            console.error('Chat API: Stream processing error:', streamError);
            res.write(`data: ${JSON.stringify({ error: streamError.message })}\n\n`);
            res.end();
          } finally {
            reader.releaseLock();
          }
        };

        processStream();
      } catch (streamSetupError) {
        console.error('Chat API: Stream setup error:', streamSetupError);
        if (!res.headersSent) {
          res.status(500).json({ error: streamSetupError.message });
        }
      }

      // IMPORTANTE: No retornar nada cuando usas @Res()
    } catch (error) {
      console.error('Chat API Error:', error);
      console.error('Error stack:', error.stack);
      if (!res.headersSent) {
        res.status(500).json({ error: error.message });
      }
    }
  }
}

export default ChatController;
