import { <PERSON>, Post, Body, Res } from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiBody, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { ChatService } from './chat.service';
import { ChatStreamRequestSchema, IChatMessage } from '@shinrai-way-of-work/models';
import { createZodDto, ZodValidationPipe } from 'nestjs-zod';
import { CoreMessage } from 'ai';

@ApiTags('Chat')
@Controller('chat')
@ApiBearerAuth()
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('stream')
  @ApiOperation({
    summary: 'Generate streaming response from <PERSON>',
    description:
      'Sends an array of messages to <PERSON> and returns a streaming response using Server-Sent Events.',
  })
  @ApiResponse({ status: 200, description: 'Streaming response from <PERSON>' })
  @ApiBody({ type: createZodDto(ChatStreamRequestSchema), required: true })
  async streamChat(
    @Body(new ZodValidationPipe(ChatStreamRequestSchema)) data: CoreMessage[],
    @Res() res: Response<unknown>
  ) {
    const result = await this.chatService.streamChat(data);

    res.set({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
    });

    res.flushHeaders();
    return result.pipeDataStreamToResponse(res);
  }
}

export default ChatController;
