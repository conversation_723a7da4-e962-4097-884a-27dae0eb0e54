import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  <PERSON>s,
  Sse,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { ChatService } from './chat.service';
import { ChatStreamingService } from './chat-streaming.service';
import {
  CreateChatMessageDtoClass,
  UpdateChatMessageDtoClass,
  ChatMessageQueryDtoClass,
  CreateChatMessageDto,
} from '@shinrai-way-of-work/models';
import * as SDK from '@team_yumi/node-sdk';
import { Observable } from 'rxjs';

@ApiTags('Chat Messages')
@Controller('chat')
@ApiBearerAuth()
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly chatStreamingService: ChatStreamingService
  ) {}

  @Post('messages')
  @ApiOperation({
    summary: 'Create a new chat message',
    description:
      'Creates a new chat message with the specified role (user or assistant) and content.',
  })
  @ApiBody({
    type: CreateChatMessageDtoClass,
    description: 'Chat message data to create',
  })
  @ApiCreatedResponse({
    status: 201,
    description: 'The chat message has been successfully created.',
    schema: {
      example: {
        _id: '507f1f77bcf86cd799439011',
        id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        role: 'user',
        content: 'Hello, how can I help you?',
        isStreaming: false,
        is_active: true,
        created_at: '2023-12-01T10:00:00.000Z',
        updated_at: '2023-12-01T10:00:00.000Z',
      },
    },
  })
  @ApiBadRequestResponse({
    status: 400,
    description: 'Bad Request - Invalid input data.',
    schema: {
      example: {
        statusCode: 400,
        message: ['Content is required', 'Role must be either user or assistant'],
        error: 'Bad Request',
      },
    },
  })
  async create(@Body() createChatMessageDto: CreateChatMessageDtoClass) {
    return await this.chatService.createMessage(createChatMessageDto);
  }

  @Get('messages')
  @ApiOperation({
    summary: 'Get all chat messages with pagination and filters',
    description:
      'Retrieves a paginated list of chat messages with optional filtering by role and streaming status.',
  })
  @ApiOkResponse({
    status: 200,
    description: 'Successfully retrieved chat messages.',
    schema: {
      example: {
        messages: [
          {
            _id: '507f1f77bcf86cd799439011',
            id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            role: 'user',
            content: 'Hello, how can I help you?',
            isStreaming: false,
            is_active: true,
            created_at: '2023-12-01T10:00:00.000Z',
            updated_at: '2023-12-01T10:00:00.000Z',
          },
        ],
        total: 1,
      },
    },
  })
  @ApiQuery({
    name: 'role',
    required: false,
    enum: ['user', 'assistant'],
    description: 'Filter messages by role',
  })
  @ApiQuery({
    name: 'isStreaming',
    required: false,
    type: Boolean,
    description: 'Filter messages by streaming status',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of messages to return (max 100, default 20)',
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of messages to skip (default 0)',
  })
  async findAll(@Query() query: ChatMessageQueryDtoClass) {
    return await this.chatService.findAll(query);
  }

  @Get('messages/:id')
  @ApiOperation({
    summary: 'Get a chat message by id',
    description: 'Retrieves a specific chat message by its unique identifier.',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique chat message identifier (UUID)',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiOkResponse({
    status: 200,
    description: 'Successfully retrieved the chat message.',
    schema: {
      example: {
        _id: '507f1f77bcf86cd799439011',
        id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        role: 'user',
        content: 'Hello, how can I help you?',
        isStreaming: false,
        is_active: true,
        created_at: '2023-12-01T10:00:00.000Z',
        updated_at: '2023-12-01T10:00:00.000Z',
      },
    },
  })
  @ApiNotFoundResponse({
    status: 404,
    description: 'Chat message not found.',
    schema: {
      example: {
        statusCode: 404,
        message: 'Chat message with id a1b2c3d4-e5f6-7890-abcd-ef1234567890 not found',
        error: 'Not Found',
      },
    },
  })
  async findOne(@Param('id') id: string) {
    return await this.chatService.findOne(id);
  }

  @Patch('messages/:id')
  @ApiOperation({ summary: 'Update a chat message' })
  @ApiResponse({ status: 200, description: 'The chat message has been successfully updated.' })
  @ApiResponse({ status: 404, description: 'Chat message not found.' })
  @ApiParam({ name: 'id', description: 'Chat message ID' })
  async update(@Param('id') id: string, @Body() updateChatMessageDto: UpdateChatMessageDtoClass) {
    return await this.chatService.update(id, updateChatMessageDto);
  }

  @Delete('messages/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a chat message (soft delete)' })
  @ApiResponse({ status: 204, description: 'The chat message has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Chat message not found.' })
  @ApiParam({ name: 'id', description: 'Chat message ID' })
  async remove(@Param('id') id: string) {
    await this.chatService.remove(id);
  }

  @Get('messages/role/:role')
  @ApiOperation({ summary: 'Get messages by role' })
  @ApiResponse({ status: 200, description: 'Return messages by role.' })
  @ApiParam({ name: 'role', enum: ['user', 'assistant'] })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async findByRole(@Param('role') role: 'user' | 'assistant', @Query('limit') limit?: number) {
    return await this.chatService.findByRole(role, limit);
  }

  @Get('messages/streaming')
  @ApiOperation({ summary: 'Get all streaming messages' })
  @ApiResponse({ status: 200, description: 'Return all streaming messages.' })
  async findStreamingMessages() {
    return await this.chatService.findStreamingMessages();
  }

  @Post('messages/stream')
  @ApiOperation({
    summary: 'Create a streaming chat message',
    description: 'Creates a new chat message with streaming enabled for real-time updates.',
  })
  @ApiBody({
    type: CreateChatMessageDtoClass,
    description: 'Chat message data to create with streaming',
  })
  @ApiCreatedResponse({
    status: 201,
    description: 'Streaming message created successfully.',
  })
  async createStreamingMessage(@Body() createChatMessageDto: CreateChatMessageDtoClass) {
    const messageGenerator = this.chatStreamingService.createStreamingMessage(createChatMessageDto);
    const result = await messageGenerator.next();
    return result.value;
  }

  @Patch('messages/:id/stream')
  @ApiOperation({
    summary: 'Update streaming message content',
    description: 'Updates the content of a streaming message and optionally completes the stream.',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique chat message identifier (UUID)',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        content: { type: 'string', description: 'New content for the message' },
        isComplete: { type: 'boolean', description: 'Whether to complete the streaming' },
        error: { type: 'string', description: 'Error message if any' },
      },
    },
  })
  async updateStreamingMessage(
    @Param('id') id: string,
    @Body() updateData: { content: string; isComplete?: boolean; error?: string }
  ) {
    return await this.chatStreamingService.updateStreamingMessage(
      id,
      updateData.content,
      updateData.isComplete,
      updateData.error
    );
  }

  @Post('messages/:id/complete')
  @ApiOperation({
    summary: 'Complete a streaming message',
    description: 'Marks a streaming message as complete and stops the streaming.',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique chat message identifier (UUID)',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        finalContent: { type: 'string', description: 'Final content for the message' },
        error: { type: 'string', description: 'Error message if any' },
      },
    },
  })
  async completeStreamingMessage(
    @Param('id') id: string,
    @Body() completeData: { finalContent?: string; error?: string }
  ) {
    return await this.chatStreamingService.completeStreamingMessage(
      id,
      completeData.finalContent,
      completeData.error
    );
  }
}

export default ChatController;
