import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ChatService } from './chat.service';
import {
  CreateChatMessageDtoClass,
  UpdateChatMessageDtoClass,
  ChatMessageQueryDtoClass,
} from '@shinrai-way-of-work/models';
import * as SDK from '@team_yumi/node-sdk';

@ApiTags('Chat Messages')
@Controller('chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('messages')
  @ApiOperation({ summary: 'Create a new chat message' })
  @ApiResponse({ status: 201, description: 'The chat message has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async create(@Body() createChatMessageDto: CreateChatMessageDtoClass) {
    return await this.chatService.createMessage(createChatMessageDto);
  }

  @Get('messages')
  @ApiOperation({ summary: 'Get all chat messages with pagination and filters' })
  @ApiResponse({ status: 200, description: 'Return all chat messages.' })
  @ApiQuery({ name: 'role', required: false, enum: ['user', 'assistant'] })
  @ApiQuery({ name: 'isStreaming', required: false, type: Boolean })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  async findAll(@Query() query: ChatMessageQueryDtoClass) {
    return await this.chatService.findAll(query);
  }

  @Get('messages/:id')
  @ApiOperation({ summary: 'Get a chat message by id' })
  @ApiResponse({ status: 200, description: 'Return the chat message.' })
  @ApiResponse({ status: 404, description: 'Chat message not found.' })
  @ApiParam({ name: 'id', description: 'Chat message ID' })
  async findOne(@Param('id') id: string) {
    return await this.chatService.findOne(id);
  }

  @Patch('messages/:id')
  @ApiOperation({ summary: 'Update a chat message' })
  @ApiResponse({ status: 200, description: 'The chat message has been successfully updated.' })
  @ApiResponse({ status: 404, description: 'Chat message not found.' })
  @ApiParam({ name: 'id', description: 'Chat message ID' })
  async update(
    @Param('id') id: string,
    @Body() updateChatMessageDto: UpdateChatMessageDtoClass,
  ) {
    return await this.chatService.update(id, updateChatMessageDto);
  }

  @Delete('messages/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a chat message (soft delete)' })
  @ApiResponse({ status: 204, description: 'The chat message has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Chat message not found.' })
  @ApiParam({ name: 'id', description: 'Chat message ID' })
  async remove(@Param('id') id: string) {
    await this.chatService.remove(id);
  }

  @Get('messages/role/:role')
  @ApiOperation({ summary: 'Get messages by role' })
  @ApiResponse({ status: 200, description: 'Return messages by role.' })
  @ApiParam({ name: 'role', enum: ['user', 'assistant'] })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async findByRole(
    @Param('role') role: 'user' | 'assistant',
    @Query('limit') limit?: number,
  ) {
    return await this.chatService.findByRole(role, limit);
  }

  @Get('messages/streaming')
  @ApiOperation({ summary: 'Get all streaming messages' })
  @ApiResponse({ status: 200, description: 'Return all streaming messages.' })
  async findStreamingMessages() {
    return await this.chatService.findStreamingMessages();
  }
}

export default ChatController;
