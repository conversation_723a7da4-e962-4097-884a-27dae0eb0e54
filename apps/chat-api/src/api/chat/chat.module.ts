import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ChatService } from './chat.service';
import { ChatController } from './chat.controller';
import secrets from '../../config/secrets';

@Module({
  imports: [HttpModule.register({ baseURL: secrets.URL_CHAT_API })],
  controllers: [ChatController],
  providers: [ChatService],
})
export class ChatModule {}
