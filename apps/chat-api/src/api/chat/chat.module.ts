import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatService } from './chat.service';
import { ChatStreamingService } from './chat-streaming.service';
import { AIService } from './ai.service';
import ChatController from './chat.controller';
import { ChatMessage, ChatMessageSchema } from '@shinrai-way-of-work/models';

@Module({
  imports: [MongooseModule.forFeature([{ name: ChatMessage.name, schema: ChatMessageSchema }])],
  controllers: [ChatController],
  providers: [ChatService, ChatStreamingService, AIService],
  exports: [ChatService, ChatStreamingService, AIService],
})
export class ChatModule {}
