import { Modu<PERSON>, Provider } from '@nestjs/common';
import { JwtModule } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import path from 'path';
import { HealthModule } from './health/health.module';
import { ChatModule } from './chat/chat.module';
import { MongoModule } from '@shinrai-way-of-work/databases';
import secrets from '../config/secrets';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ExceptionInterceptor } from '@shinrai-way-of-work/utils';
import { LogLevel, NestJS } from '@team_seki/winston-logger';

export const PROVIDERS: Provider[] = [
  {
    provide: APP_INTERCEPTOR,
    useClass: ExceptionInterceptor,
  },
];

@Module({
  imports: [
    HealthModule,
    ChatModule,
    JwtModule.forRoot({
      publicKeyPath: path.join(__dirname, 'config', 'public_key.pub'),
      allowedIssuer: 'shinrai',
      expiration_in_minutes: 60,
      algorithm: 'RS512',
    }),
    MongoModule.forRoot({
      database: 'db-chat',
      environment: secrets.ENVIROMENT,
    }),
    NestJS.LoggerModule.forRoot({
      appName: secrets.PRODUCT_NAME,
      environment: process.env.NODE_ENV,
      level: secrets.LOG_LEVEL as LogLevel,
      outputFormat: process.env.NODE_ENV == 'development' ? 'text' : 'json',
    }),
  ],
  providers: [...PROVIDERS],
})
export class ApiModule {}
