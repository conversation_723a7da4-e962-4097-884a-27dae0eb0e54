import { v4 as uuidv4 } from 'uuid';

/**
 * Generates a unique ID for chat messages
 */
export function generateMessageId(): string {
  return uuidv4();
}

/**
 * Validates if a message role is valid
 */
export function isValidRole(role: string): role is 'user' | 'assistant' {
  return role === 'user' || role === 'assistant';
}

/**
 * Sanitizes message content
 */
export function sanitizeContent(content: string): string {
  return content.trim();
}
