import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { patchNestJsSwagger } from 'nestjs-zod';
import { ApiModule } from './api/api.module';
import secrets from './config/secrets';
import { NestJS } from '@team_seki/winston-logger';

async function bootstrap() {
  const app = await NestFactory.create(ApiModule);
  const port = secrets.PORT_CHAT_API || 8080;
  app.useLogger(app.get(NestJS.NestLogger));
  app.enableCors();
  patchNestJsSwagger();

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Chat API')
    .setDescription('API for managing chat messages with support for real-time streaming')
    .setVersion('1.0')
    .addTag('Chat Messages', 'Operations related to chat messages')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(port);

  Logger.log(`🚀 Chat API is running on: http://localhost:${port}`, secrets.PRODUCT_NAME);
  Logger.log(
    `📚 Swagger documentation available at: http://localhost:${port}/api/docs`,
    secrets.PRODUCT_NAME
  );
}

bootstrap();
