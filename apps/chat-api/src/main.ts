import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { patchNestJsSwagger } from 'nestjs-zod';
import { ApiModule } from './api/api.module';
import secrets from './config/secrets';
import { NestJS } from '@team_seki/winston-logger';

async function bootstrap() {
  const app = await NestFactory.create(ApiModule);
  const port = secrets.PORT_CHAT_API || 8080;
  app.useLogger(app.get(NestJS.NestLogger));
  app.enableCors();
  patchNestJsSwagger();
  await app.listen(port);

  Logger.log(`🚀 Chat API is running on: http://localhost:${port}`, secrets.PRODUCT_NAME);
}

bootstrap();
