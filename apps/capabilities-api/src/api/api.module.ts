import { Module, Provider } from '@nestjs/common';
import { HealthModule } from './health/health.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ExceptionInterceptor } from '@shinrai-way-of-work/utils';
import { JwtModule } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { MongoModule } from '@shinrai-way-of-work/databases';
import secrets from '../config/secrets';
import { NestJS, LogLevel } from '@team_seki/winston-logger';
import path = require('path');
import { CapabilityModule } from './capability/capability.module';

export const PROVIDERS: Provider[] = [
  {
    provide: APP_INTERCEPTOR,
    useClass: ExceptionInterceptor,
  },
  //  {
  //     provide: APP_PIPE,
  //      useClass: ZodValidationPipe,
  //   },
];

@Module({
  imports: [
    HealthModule,
    CapabilityModule,
    JwtModule.forRoot({
      //allowedIssuer: 'shinrai',
      //privateKeyPath: path.join(__dirname, 'config', 'private_key.pem'),
      publicKeyPath: path.join(__dirname, 'config', 'public_key.pub'),
      expiration_in_minutes: 60,
      algorithm: 'RS512',
    }),
    MongoModule.forRoot({
      database: 'db-capabilities',
      environment: process.env.NODE_ENV || 'development',
    }),
    NestJS.LoggerModule.forRoot({
      appName: secrets.PRODUCT_NAME,
      environment: process.env.NODE_ENV,
      level: (process.env.LOG_LEVEL as LogLevel) || 'info',
      outputFormat: process.env.NODE_ENV == 'development' ? 'text' : 'json',
    }),
  ],
})

export class ApiModule {}
