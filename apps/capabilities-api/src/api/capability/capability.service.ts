import * as SDK from '@team_yumi/node-sdk';
import { Injectable, Logger } from "@nestjs/common";
import { FilterQuery, Types } from 'mongoose';
import { CapabilityRepository } from "@shinrai-way-of-work/core";
import { Capability, CapabilityFiltersDto, ICapability, IShared } from "@shinrai-way-of-work/models";
import { ApiResponse, COMMON_ERRORS_CODE, doTryCatch } from "@shinrai-way-of-work/utils";

@Injectable()
export class CapabilityService {
  logger: Logger;
  constructor(
    private readonly capabilityRepository: CapabilityRepository,
  ) {
    this.logger = new Logger(CapabilityService.name);
  }

  async find(dto: CapabilityFiltersDto.QueryDto) {
    return doTryCatch(this.logger, async () => {
      dto.is_active = !dto.is_active ? "true" : dto.is_active;
      dto.limit = dto.limit ? dto.limit : 10;
      dto.offset = dto.offset ? dto.offset : 0;

      const { offset, limit } = dto;
      const filters: FilterQuery<Capability> = {
        ...(dto.funcionalidad && { funcionalidad: { $regex: dto.funcionalidad, $options: 'i' } }),
        ...(dto.definicion && { definicion: { $regex: dto.definicion, $options: 'i' } }),
        ...(dto.dominio && { dominio: { $regex: dto.dominio, $options: 'i' } }),
        ...(dto.capacidad && { capacidad: { $regex: dto.capacidad, $options: 'i' } }),
        ...(dto.sub_capacidad && { sub_capacidad: { $regex: dto.sub_capacidad, $options: 'i' } }),
        ...(dto.foco_operativo && { foco_operativo: { $regex: dto.foco_operativo, $options: 'i' } }),
        ...(dto.cadena && { cadena: { $regex: dto.cadena, $options: 'i' } }),
        ...(dto.proceso && { proceso: { $regex: dto.proceso, $options: 'i' } }),
        ...(dto.responsable && { responsable: { $regex: dto.responsable, $options: 'i' } }),
        ...(dto.is_active && { is_active: { $eq: dto.is_active === "true" } }),
        ...(dto.id && { _id: new Types.ObjectId(dto.id) }),
      };

      const allCapabilities = await this.capabilityRepository.findPageable({
        query: filters,
        paginationOptions: {
          offset,
          size: limit
        },
        projection: {
          created_at: 0,
          updated_at: 0,
          updated_by: 0,
          created_by: 0,
          is_active: 0,
          __v: 0
        }
      });

      return ApiResponse.TruePaginate({
        data: allCapabilities.data,
        metadata: allCapabilities.metadata,
        statusCode: 200,
      });
    });
  }

  async create(data: ICapability.CreateCapability, user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const newCapability = await this.capabilityRepository.create({
        ...data,
        created_by: {
          email: user.email,
          name: user.unique_name,
          identifier: user.primarysid,
        },
        is_active: true,
      } as any);

      return ApiResponse.OkTrue({ data: newCapability, statusCode: 201 });
    });
  }

  async createBulk(data: ICapability.CreateBulkCapability, user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const userInfo = {
        email: user.email,
        name: user.unique_name,
        identifier: user.primarysid,
      };

      const capabilitiesToCreate = data.capabilities.map(capability => ({
        ...capability,
        created_by: userInfo,
        is_active: true,
      }));

      const createdCapabilities = await this.capabilityRepository.createBulk(capabilitiesToCreate as any);

      return ApiResponse.OkTrue({ 
        data: createdCapabilities, 
        statusCode: 201,
        message: `${createdCapabilities.length} capabilities creadas exitosamente`
      });
    });
  }

  async update(dto: IShared.RequestIdDto, data: ICapability.UpdateCapability, user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;

      const capability = await this.capabilityRepository.findById({ id });
      if (!capability) {
        return ApiResponse.False({ message: 'Capability not found', statusCode: 404, errorCode: COMMON_ERRORS_CODE.NOT_FOUND });
      }

      const updateCapability = await this.capabilityRepository.update({
        query: { _id: id },
        data: {
          $set: {
            ...data,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        }
      });

      return ApiResponse.OkTrue({ data: updateCapability, statusCode: 200 });
    });
  }

  async delete(dto: IShared.RequestIdDto, user: SDK.Models.IAM.IJwtEntity) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;

      const capability = await this.capabilityRepository.findById({ id });
      if (!capability) {
        return ApiResponse.False({ message: 'Capability not found', statusCode: 404, errorCode: COMMON_ERRORS_CODE.NOT_FOUND });
      }

      await this.capabilityRepository.update({
        query: { _id: id },
        data: {
          $set: {
            is_active: false,
            updated_by: {
              email: user.email,
              name: user.unique_name,
              identifier: user.primarysid,
            },
          },
        }
      });

      return ApiResponse.OkTrue({ message: 'Capability deleted', statusCode: 200 });
    });
  }

  async findById(dto: IShared.RequestIdDto) {
    return doTryCatch(this.logger, async () => {
      const { id } = dto;

      const capability = await this.capabilityRepository.findById({ 
        id,
        projection: {
          created_at: 0,
          updated_at: 0,
          updated_by: 0,
          created_by: 0,
          __v: 0
        }
      });

      if (!capability) {
        return ApiResponse.False({ message: 'Capability not found', statusCode: 404, errorCode: COMMON_ERRORS_CODE.NOT_FOUND });
      }

      return ApiResponse.OkTrue({ data: capability, statusCode: 200 });
    });
  }
} 