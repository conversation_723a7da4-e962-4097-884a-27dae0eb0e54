import * as SDK from '@team_yumi/node-sdk';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Res, UseInterceptors } from "@nestjs/common";
import { Response } from 'express';
import { CapabilityService } from "./capability.service";
import { Authenticated, User } from '@team_yumi/node-sdk/src/lib/nest/modules/jwt';
import { CapabilityDto, CapabilityFiltersDto, ICapability, IShared } from "@shinrai-way-of-work/models";
import { ZodValidationPipe } from '@shinrai-way-of-work/core';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";
import { createZodDto } from "nestjs-zod";
import { QueryValidationInterceptor } from "@shinrai-way-of-work/utils";

class CreateCapabilityDto extends createZodDto(CapabilityDto.CreateSchema.omit({})) {}
class UpdateCapabilityDto extends createZodDto(CapabilityDto.UpdateSchema) {}
class CreateBulkCapabilityDto extends createZodDto(CapabilityDto.CreateBulkSchema) {}

@Authenticated()
@ApiBearerAuth('defaultBearerAuth')
@ApiTags('capabilities')
@Controller('capabilities')
export class CapabilityController {
  constructor(
    private readonly capabilityService: CapabilityService
  ) {}

  @Get('')
  @ApiOperation({ summary: 'Get all capabilities' })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  @ApiQuery({ name: 'funcionalidad', required: false })
  @ApiQuery({ name: 'definicion', required: false })
  @ApiQuery({ name: 'dominio', required: false })
  @ApiQuery({ name: 'capacidad', required: false })
  @ApiQuery({ name: 'sub_capacidad', required: false })
  @ApiQuery({ name: 'foco_operativo', required: false })
  @ApiQuery({ name: 'cadena', required: false })
  @ApiQuery({ name: 'proceso', required: false })
  @ApiQuery({ name: 'responsable', required: false })
  @ApiQuery({ name: 'is_active', required: false })
  @ApiQuery({ name: 'id', required: false })
  @ApiResponse({ status: 200, description: 'Return all capabilities' })
  @UseInterceptors(new QueryValidationInterceptor(CapabilityFiltersDto.QuerySchema))
  async find(@Query() query: CapabilityFiltersDto.QueryDto, @Res() res: Response<unknown>) {
    const result = await this.capabilityService.find(query);
    return res.status(result.statusCode).json(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get capability by id' })
  @ApiParam({ name: 'id', required: true, description: 'capability id', type: 'string' })
  @ApiResponse({ status: 200, description: 'Return capability by id' })
  async findById(@Param() dto: IShared.RequestIdDto, @Res() res: Response<unknown>) {
    const result = await this.capabilityService.findById(dto);
    return res.status(result.statusCode).json(result);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new capability' })
  @ApiResponse({ status: 201, description: 'Capability created' })
  @ApiBody({ type: CreateCapabilityDto, required: true })
  async create(
    @Body(new ZodValidationPipe(CapabilityDto.CreateSchema)) data: ICapability.CreateCapability, 
    @Res() res: Response<unknown>,
    @User() user: SDK.Models.IAM.IJwtEntity,
  ) {
    console.log(data)
    const result = await this.capabilityService.create(data, user);
    return res.status(result.statusCode).json(result);
  }

  @Post('bulk')
  @ApiOperation({ summary: 'Create multiple capabilities' })
  @ApiResponse({ status: 201, description: 'Capabilities created successfully' })
  @ApiBody({ type: CreateBulkCapabilityDto, required: true })
  async createBulk(
    @Body(new ZodValidationPipe(CapabilityDto.CreateBulkSchema)) data: ICapability.CreateBulkCapability, 
    @Res() res: Response<unknown>,
    @User() user: SDK.Models.IAM.IJwtEntity,
  ) {
    const result = await this.capabilityService.createBulk(data, user);
    return res.status(result.statusCode).json(result);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update capability by id' })
  @ApiParam({ name: 'id', required: true, description: 'capability id', type: 'string' })
  @ApiResponse({ status: 200, description: 'Update capability by id' })
  @ApiBody({ type: UpdateCapabilityDto, required: true })
  async update(
    @Param() dto: IShared.RequestIdDto, 
    @Body(new ZodValidationPipe(CapabilityDto.UpdateSchema)) data: ICapability.UpdateCapability, 
    @User() user: SDK.Models.IAM.IJwtEntity,
    @Res() res: Response<unknown>
  ) {
    const result = await this.capabilityService.update(dto, data, user);
    return res.status(result.statusCode).json(result);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete capability by id' })
  @ApiParam({ name: 'id', required: true, description: 'capability id', type: 'string' })
  @ApiResponse({ status: 200, description: 'Delete capability by id' })
  async delete(
    @Param() dto: IShared.RequestIdDto, 
    @Res() res: Response<unknown>,
    @User() user: SDK.Models.IAM.IJwtEntity,
  ) {
    const result = await this.capabilityService.delete(dto, user);
    return res.status(result.statusCode).json(result);
  }
} 
