import { Module } from "@nestjs/common";
import { CapabilityController } from "./capability.controller";
import { CapabilityService } from "./capability.service";
import { CapabilityRepositoryModule } from "@shinrai-way-of-work/core";

@Module({
  controllers: [CapabilityController],
  providers: [CapabilityService],
  imports: [
    CapabilityRepositoryModule.register(),
  ]
})
export class CapabilityModule {} 