import { Controller, Get } from '@nestjs/common';
import * as SDK from '@team_yumi/node-sdk';
import { HealthService } from './health.service';

@SDK.Lib.Nest.Modules.Jwt.Public()
@Controller()
export class HealthController {
  constructor(private readonly service: HealthService) { }

  @Get("health")
  health() {
    return this.service.health();
  }

  @Get("ping")
  ping() {
    return this.service.ping();
  }
}

export default HealthController;
