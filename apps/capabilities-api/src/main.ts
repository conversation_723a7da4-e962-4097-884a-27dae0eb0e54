import { Logger } from '@nestjs/common';
import * as express from 'express';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ApiModule } from './api/api.module';
import secrets from './config/secrets';
import { patchNestJsSwagger } from 'nestjs-zod';
import { NestJS } from '@team_seki/winston-logger';

async function bootstrap() {
  const app = await NestFactory.create(ApiModule, { cors: true });
  const port = 8080;
  app.enableCors();
  app.use(express.json({ limit: '10mb' }));
  app.useLogger(app.get(NestJS.NestLogger));

  const config = new DocumentBuilder()
    .setTitle('Shinrai way of work')
    .setDescription('documentation of all API endpoints')
    .setVersion('1.0')
    .addBearerAuth(undefined, 'defaultBearerAuth')
    .build();
  patchNestJsSwagger();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      authAction: {
        defaultBearerAuth: {
          name: 'defaultBearerAuth',
          schema: {
            description: 'Default',
            type: 'http',
            in: 'header',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
          value: '<JWT>',
        },
      },
    },
  });

  await app.listen(port);

  Logger.log(`🚀 Api is running on: http://localhost:${port}`, secrets.PRODUCT_NAME);
}

bootstrap();
