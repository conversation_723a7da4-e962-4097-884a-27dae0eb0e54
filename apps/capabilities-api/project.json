{"name": "capabilities-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/capabilities-api/src", "projectType": "application", "targets": {"build": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "release", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/capabilities-api", "main": "apps/capabilities-api/src/main.ts", "tsConfig": "apps/capabilities-api/tsconfig.app.json", "assets": ["apps/capabilities-api/src/config"], "webpackConfig": "apps/capabilities-api/webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "inspect": true}, "release": {"optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": true, "inspect": false}}}, "serve": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "capabilities-api:build"}, "configurations": {"development": {"buildTarget": "capabilities-api:build:development"}, "release": {"buildTarget": "capabilities-api:build:release", "hmr": false}}}, "lint": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/capabilities-api/**/*.ts"]}}, "test": {"dependsOn": [{"target": "secrets"}], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/capabilities-api"], "options": {"jestConfig": "apps/capabilities-api/jest.config.ts", "passWithNoTests": true}}, "secrets": {"executor": "nx:run-commands", "options": {"command": "seki secrets generate -p=capabilities-api", "cwd": "."}}}, "tags": ["REQUIRED:<PERSON>LDEN"]}