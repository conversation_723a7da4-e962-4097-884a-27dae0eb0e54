{"name": "@shinrai-way-of-work/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "devDependencies": {"@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/eslint-plugin": "16.10.0", "@nx/jest": "16.10.0", "@nx/js": "16.10.0", "@nx/linter": "16.10.0", "@nx/nest": "^16.10.0", "@nx/node": "16.10.0", "@nx/webpack": "16.10.0", "@nx/workspace": "16.10.0", "@swc-node/register": "^1.10.10", "@swc/core": "^1.12.9", "@types/jest": "^29.4.0", "@types/lodash": "^4.17.0", "@types/multer": "^1.4.11", "@types/node": "~18.7.1", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "eslint": "~8.46.0", "eslint-config-prettier": "8.1.0", "jest": "^29.4.1", "jest-environment-node": "^29.4.1", "nx": "16.10.0", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.1.3", "webpack-merge": "^5.9.0"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@google-cloud/logging-winston": "^6.0.0", "@google-cloud/storage": "^7.12.0", "@nestjs/apollo": "^12.2.2", "@nestjs/axios": "^3.0.1", "@nestjs/common": "^10.0.2", "@nestjs/core": "^10.0.2", "@nestjs/graphql": "^12.2.2", "@nestjs/mongoose": "^10.0.10", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.2", "@nestjs/swagger": "^7.1.16", "@team_seki/dev-kit": "^1.1.5", "@team_seki/google-bucket-plugin": "^1.6.0", "@team_seki/kafka-streamer-plugin": "^1.5.0", "@team_seki/mongodb-plugin": "^1.6.0", "@team_seki/pubsub-streamer-plugin": "^1.5.2", "@team_seki/subscriber-plugin": "^1.5.0", "@team_seki/winston-logger": "^0.0.1", "@team_yumi/node-sdk": "^0.0.2", "@wahyubucil/nestjs-zod-openapi": "^0.1.2", "ai": "^4.3.17", "axios": "^1.6.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "date-fns": "^3.6.0", "exceljs": "^4.4.0", "json2csv": "^6.0.0-alpha.2", "lodash": "^4.17.21", "mongoose": "^6.8.2", "multer": "^1.4.5-lts.1", "nest-winston": "^1.9.7", "nestjs-cls": "^4.3.0", "nestjs-mediator": "^0.0.5", "nestjs-zod": "^3.0.0", "passport": "^0.7.0", "passport-headerapikey": "^1.2.2", "percentile": "^1.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "swagger-ui-express": "^5.0.0", "tslib": "^2.3.0", "winston": "^3.15.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "overrides": {"@nestjs/common": "^10.0.2", "@nestjs/core": "^10.0.2"}}