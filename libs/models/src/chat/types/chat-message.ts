export interface ChatMessage {
  id: string; // único por mensaje
  role: 'user' | 'assistant';
  content: string;
  createdAt: Date;
  isStreaming?: boolean; // indica si el mensaje del bot está siendo construido en tiempo real
  error?: string; // para almacenar errores por mensaje (opcional)
}

export type ChatMessageRole = 'user' | 'assistant';

export type CreateChatMessage = {
  role: ChatMessageRole;
  content: string;
  isStreaming?: boolean;
  error?: string;
};

export type UpdateChatMessage = Partial<CreateChatMessage> & {
  isStreaming?: boolean;
  error?: string;
};
