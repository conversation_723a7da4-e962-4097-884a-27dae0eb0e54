import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { UserData } from '../../shared/common';

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class ChatMessage {
  _id?: string;

  @Prop({ required: true, unique: true })
  id!: string; // UUID único por mensaje

  @Prop({ required: true, enum: ['user', 'assistant'] })
  role!: string;

  @Prop({ required: true, maxlength: 10000 })
  content!: string;

  @Prop({ required: false, default: false })
  isStreaming?: boolean;

  @Prop({ required: false })
  error?: string;

  @Prop({ type: UserData, required: false })
  created_by?: UserData;

  @Prop({ type: UserData, required: false })
  updated_by?: UserData;

  @Prop({ required: true, default: true })
  is_active?: boolean;

  created_at?: Date;
  updated_at?: Date;
}

export type ChatMessageDocument = ChatMessage;
export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage);

// Índices para optimizar consultas
ChatMessageSchema.index({ id: 1 }, { unique: true });
ChatMessageSchema.index({ role: 1 });
ChatMessageSchema.index({ created_at: -1 });
ChatMessageSchema.index({ isStreaming: 1 });
