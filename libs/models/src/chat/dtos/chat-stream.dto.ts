import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

export const ChatStreamMessageSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string().min(1, 'Content is required').max(10000, 'Content is too long'),
});

export const ChatStreamRequestSchema = z.object({
  messages: z.array(ChatStreamMessageSchema).min(1, 'At least one message is required'),
  maxTokens: z.number().min(1).max(4000).optional().default(1000),
  temperature: z.number().min(0).max(2).optional().default(0.7),
});

export type ChatStreamMessage = z.infer<typeof ChatStreamMessageSchema>;
export type ChatStreamRequest = z.infer<typeof ChatStreamRequestSchema>;

// NestJS DTOs using nestjs-zod
export class ChatStreamMessageDtoClass extends createZodDto(ChatStreamMessageSchema) {}
export class ChatStreamRequestDtoClass extends createZodDto(ChatStreamRequestSchema) {}
