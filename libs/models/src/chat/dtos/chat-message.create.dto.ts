import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

export const ChatMessageRoleEnum = z.enum(['user', 'assistant']);

export const CreateChatMessageSchema = z.object({
  role: ChatMessageRoleEnum,
  content: z.string().min(1, 'Content is required').max(10000, 'Content is too long'),
  isStreaming: z.boolean().optional().default(false),
  error: z.string().optional(),
});

export const UpdateChatMessageSchema = CreateChatMessageSchema.partial().extend({
  isStreaming: z.boolean().optional(),
  error: z.string().optional(),
});

export const ChatMessageQuerySchema = z.object({
  role: ChatMessageRoleEnum.optional(),
  isStreaming: z.boolean().optional(),
  limit: z.number().min(1).max(100).optional().default(20),
  offset: z.number().min(0).optional().default(0),
});

export type CreateChatMessageDto = z.infer<typeof CreateChatMessageSchema>;
export type UpdateChatMessageDto = z.infer<typeof UpdateChatMessageSchema>;
export type ChatMessageQueryDto = z.infer<typeof ChatMessageQuerySchema>;

// NestJS DTOs using nestjs-zod
export class CreateChatMessageDtoClass extends createZodDto(CreateChatMessageSchema) {}
export class UpdateChatMessageDtoClass extends createZodDto(UpdateChatMessageSchema) {}
export class ChatMessageQueryDtoClass extends createZodDto(ChatMessageQuerySchema) {}
