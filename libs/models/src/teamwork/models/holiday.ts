import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { UserData } from '../../shared';
import { Types } from 'mongoose';

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Holiday {
  _id?: string;

  @Prop({ required: false })
  name?: string;

  @Prop({ required: true })
  date!: Date;

  @Prop({ type: Types.ObjectId, ref: 'ExchangeRate' })
  country_id!: Types.ObjectId;

  @Prop({ type: UserData, required: false })
  created_by?: UserData;

  @Prop({ type: UserData, required: false })
  updated_by?: UserData;
}

export type HolidayDocument = Holiday;
export const HolidaySchema = SchemaFactory.createForClass(Holiday);
