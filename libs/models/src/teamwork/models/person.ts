import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { UserData } from '../../shared';
import mongoose, { Types } from 'mongoose';

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Person {
  @Prop({ required: true, unique: true, index: true })
  id!: number;

  @Prop({ required: false, unique: true })
  id_entryRequest?: number;

  _id?: string;

  @Prop({ required: true })
  name!: string;

  @Prop({ required: false })
  lastName?: string;

  @Prop({ required: false })
  fullName?: string;

  @Prop({ required: false })
  RUT?: string;

  @Prop({ required: false })
  birthDate?: string;

  @Prop({ required: true })
  gender!: string;

  @Prop({ required: false })
  nationality?: string;

  @Prop({ required: false })
  position?: string;

  @Prop({ required: false })
  period?: number;

  @Prop({ required: false })
  entryDate?: string;

  @Prop({ required: false })
  exitDate?: string;

  @Prop({ required: false })
  SAPcode?: string;

  @Prop({ required: false })
  MATdescription?: string;

  @Prop({ required: false })
  budget_line?: string;

  @Prop({ required: false })
  budget_type?: string;

  @Prop({ required: false })
  ceco?: string;

  @Prop({ required: false })
  purchaseOrder?: string;

  @Prop({ required: false })
  serviceType?: string;

  @Prop({ required: false })
  hour?: number;

  @Prop({ required: false })
  workType?: string;

  @Prop({ required: false, type: mongoose.Schema.Types.ObjectId, ref: 'roleteamwork', index: true })
  roleId?: string;

  @Prop({ required: false })
  codeAd?: string;

  @Prop({
    required: false,
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AreaTeamwork',
    index: true,
  })
  areaId?: string;

  @Prop({
    required: false,
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubareaTeamWork',
    index: true,
  })
  subareaId?: string;

  @Prop({
    required: false,
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubManagement',
    index: true,
  })
  submanagementId?: string;

  @Prop({ required: false })
  areaManager?: string;

  @Prop({ required: false })
  manager?: string;

  @Prop({ required: false })
  submanagementManager?: string;

  @Prop({ required: false, type: mongoose.Schema.Types.ObjectId, ref: 'partner', index: true })
  partnerId?: string | null;

  @Prop({ required: false, index: true })
  type?: string;

  @Prop({ required: false })
  seniority?: string;

  @Prop({ required: false })
  residenceCountry?: string;

  @Prop({ required: false })
  paymentCountry?: string;

  @Prop({ required: false })
  currency?: string;

  @Prop({ required: false })
  localCost?: number;

  @Prop({ required: false })
  costInDollar?: number;

  @Prop({ required: false })
  monthlyLocalCost?: number;

  @Prop({ required: false })
  monthlyCostInDollar?: number;

  @Prop({ required: false })
  rateToUsd?: number;

  @Prop({
    required: false,
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'license' }],
    default: [],
  })
  licenses?: Types.ObjectId[];

  @Prop({ required: false })
  email?: string;

  @Prop({ required: false })
  taskDescription?: string;

  @Prop({
    required: false,
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'skill' }],
    default: [],
  })
  skills?: Types.ObjectId[];

  @Prop({ required: true })
  conformity!: boolean;

  @Prop({ required: false, default: true })
  is_active?: boolean;

  @Prop({ type: UserData, required: false })
  created_by?: UserData;

  @Prop({ type: UserData, required: false })
  updated_by?: UserData;

  created_at?: Date;

  updated_at?: Date;
}

export type PersonDocument = Person;
export const PersonSchema = SchemaFactory.createForClass(Person);
