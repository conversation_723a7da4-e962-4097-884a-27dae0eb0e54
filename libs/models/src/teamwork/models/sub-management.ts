import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { UserData } from '../../shared';
import { Types } from 'mongoose';

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class SubManagement {
  _id?: string;

  @Prop({ required: true })
  name!: string;

  @Prop({ type: Types.ObjectId, ref: 'SubareaTeamWork' })
  subarea_id?: Types.ObjectId;

  @Prop({ required: false, default: true })
  is_active?: boolean;

  @Prop({ type: UserData, required: false })
  created_by?: UserData;

  @Prop({ type: UserData, required: false })
  updated_by?: UserData;
}

export type SubManagementDocument = SubManagement;
export const SubManagementSchema = SchemaFactory.createForClass(SubManagement);
