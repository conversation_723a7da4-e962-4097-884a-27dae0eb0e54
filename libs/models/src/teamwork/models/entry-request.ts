import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { UserData } from '../../shared';
import { Types } from 'mongoose';
import { TeamWorkEnum } from '@shinrai-way-of-work/utils';

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class EntryRequest {
  _id?: string;

  @Prop({ required: true, unique: true, index: true })
  id!: number;

  @Prop({ required: true })
  manager!: string;

  @Prop({ type: Types.ObjectId, ref: 'roleteamwork', required: true })
  role!: Types.ObjectId;

  @Prop({ required: true })
  budget!: number;

  @Prop({ required: true })
  budget_total!: number;

  @Prop({ required: true })
  budget_type!: string;

  @Prop({ required: true })
  budget_line!: string;

  @Prop({ required: true })
  months_long!: number;

  @Prop({ required: true })
  expected_date_start!: Date;

  @Prop()
  expected_date_end?: Date;

  @Prop({ required: true, maxlength: 100 })
  topic!: string;

  @Prop({ required: true, maxlength: 250 })
  comment!: string;

  @Prop({ type: Types.ObjectId, ref: 'AreaTeamWork' })
  area?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'SubareaTeamWork' })
  subarea?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'SubManagement' })
  submanagement?: Types.ObjectId;

  @Prop({ required: true })
  country!: string;

  @Prop({
    required: false,
    enum: Object.values(TeamWorkEnum.EntryRequestStatus),
    default: TeamWorkEnum.EntryRequestStatus.Pending,
  })
  status?: string;

  @Prop([
    {
      status: {
        type: String,
        enum: Object.values(TeamWorkEnum.EntryRequestStatus),
        required: false,
      },
      previous_status: {
        type: String,
        enum: Object.values(TeamWorkEnum.EntryRequestStatus),
        required: false,
      },
      subject: { type: String, maxlength: 100, required: false },
      comment: { type: String, maxlength: 250, required: false },
      user: { type: String, required: false },
      email: { type: String, required: false },
      role: { type: String, required: false },
      date: { type: Date, default: Date.now, required: false },
    },
  ])
  status_history?: {
    status?: string;
    previous_status?: string;
    subject?: string;
    comment?: string;
    user?: string;
    email?: string;
    role?: string;
    date?: Date;
  }[];

  @Prop({ required: false })
  request_date?: Date;

  @Prop({ type: UserData, required: false })
  created_by?: UserData;

  @Prop({ type: UserData, required: false })
  updated_by?: UserData;
}

export type EntryRequestDocument = EntryRequest;
export const EntryRequestSchema = SchemaFactory.createForClass(EntryRequest);
