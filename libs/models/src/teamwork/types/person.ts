import { TeamWorkEnum } from '@shinrai-way-of-work/utils';
import { Types } from 'mongoose';

export type Create = {
  id?: number;
  id_entryRequest?: number;
  name: string;
  lastName?: string;
  fullName?: string;
  RUT?: string;
  birthDate?: string;
  gender: string;
  nationality?: string;
  position?: string;
  period?: number;
  entryDate?: string;
  exitDate?: string;
  SAPcode?: string;
  MATdescription?: string;
  budget_line?: string;
  budget_type?: string;
  ceco?: string;
  purchaseOrder?: string;
  serviceType?: string;
  roleId?: string;
  codeAd?: string;
  areaId?: string;
  subareaId?: string;
  submanagementId?: string;
  areaManager?: string;
  manager?: string;
  submanagementManager?: string;
  partnerId?: string | null;
  type?: TeamWorkEnum.Type;
  seniority?: TeamWorkEnum.Seniority;
  residenceCountry?: string;
  paymentCountry?: string;
  currency?: string;
  localCost?: number;
  monthlyLocalCost?: number;
  monthlyCostInDollar?: number;
  rateToUsd?: number;
  hour?: number;
  workType?: TeamWorkEnum.WorkType;
  licenses?: Types.ObjectId[];
  email?: string;
  taskDescription?: string;
  skills?: Types.ObjectId[];
  conformity: boolean;
  visible?: boolean;
};

export type Update = Partial<Create>;
