export type Create = {
  manager: string;
  role: string;
  budget: number;
  budget_total?: number;
  budget_type: string;
  budget_line: string;
  months_long: number;
  expected_date_start: Date;
  expected_date_end?: Date;
  topic: string;
  comment: string;
  area: string;
  subarea: string;
  submanagement: string;
  country: string;
  status?: string;
  status_history?: {
    status?: string;
    previous_status?: string;
    subject?: string;
    comment?: string;
    user?: string;
    email?: string;
    role?: string;
    date?: Date;
  }[];
  request_date?: Date;
};

export type Update = Partial<Create>;
