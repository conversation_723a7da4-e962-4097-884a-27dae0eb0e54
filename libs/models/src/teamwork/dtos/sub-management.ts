import { z } from 'zod';

const ObjectIdRegex = /^[0-9a-fA-F]{24}$/;
export const SubManagementCreateDto = z.object({
  _id: z.string().regex(ObjectIdRegex, 'Invalid sub-management ID').optional(),
  name: z.string(),
  id_subarea: z.string().regex(ObjectIdRegex, 'Invalid subarea ID').optional(),
  is_active: z.boolean().optional(),
});

export const SubManagementUpdateDto = SubManagementCreateDto.partial();

export const SubManagementQueryDto = z.object({
  name: z.string().optional(),
  id_subarea: z.string().regex(ObjectIdRegex, 'Invalid subarea ID').optional(),
  is_active: z.enum(['true', 'false']).default('true'),
});

export type SubManagementCreateDto = z.infer<typeof SubManagementCreateDto>;
export type SubManagementUpdateDto = z.infer<typeof SubManagementUpdateDto>;
export type SubManagementQueryDto = z.infer<typeof SubManagementQueryDto>;