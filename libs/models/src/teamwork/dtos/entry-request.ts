import { z } from 'zod';
import { TeamWorkEnum } from '@shinrai-way-of-work/utils';
import { QueryPaginationSchema } from '../../shared';

const ObjectIdRegex = /^[0-9a-fA-F]{24}$/;
export const EntryRequestCreateDto = z.object({
  manager: z.string(),
  role: z.string(),
  budget: z.number(),
  budget_total: z.number().optional(),
  budget_type: z.string(),
  budget_line: z.string(),
  months_long: z.number(),
  expected_date_start: z.date({ coerce: true }),
  expected_date_end: z.date({ coerce: true }).optional(),
  topic: z.string().min(1).max(100),
  comment: z.string().min(1).max(250),
  country: z.string(),
  area: z.string().regex(ObjectIdRegex, 'Invalid area ID'),
  subarea: z.string().regex(ObjectIdRegex, 'Invalid subarea ID'),
  submanagement: z.string().regex(ObjectIdRegex, 'Invalid submanagement ID'),
  status: z.nativeEnum(TeamWorkEnum.EntryRequestStatus).optional(),
  status_history: z
    .array(
      z.object({
        status: z.nativeEnum(TeamWorkEnum.EntryRequestStatus).optional(),
        previous_status: z.nativeEnum(TeamWorkEnum.EntryRequestStatus).optional(),
        subject: z.string().min(1).max(100).optional(),
        comment: z.string().min(1).max(250).optional(),
        user: z.string().optional(),
        email: z.string().email().optional(),
        role: z.string().optional(),
        date: z.date({ coerce: true }).optional(),
      })
    )
    .optional(),
  request_date: z.date({ coerce: true }).optional(),
});

export const EntryRequestUpdateDto = EntryRequestCreateDto.partial();

export const EntryRequestQueryDto = QueryPaginationSchema.extend({
  id: z.string().regex(ObjectIdRegex, 'Invalid entry request ID').optional(),
  manager: z.string().optional(),
  role: z.string().regex(ObjectIdRegex, 'Invalid role ID').optional(),
  budget: z.number().optional(),
  budget_type: z.string().optional(),
  months_long: z.number().optional(),
  expected_date_start: z.string().optional(),
  expected_date_end: z.string().optional(),
  area: z.string().regex(ObjectIdRegex, 'Invalid area ID').optional(),
  subarea: z.string().regex(ObjectIdRegex, 'Invalid subarea ID').optional(),
  submanagement: z.string().regex(ObjectIdRegex, 'Invalid submanagement ID').optional(),
  country: z.string().optional(),
  status: z.nativeEnum(TeamWorkEnum.EntryRequestStatus).optional(),
  request_date: z.string().optional(),
  search: z.string().optional(),
});

export const StatusCommentDto = z.object({
  subject: z.string().min(1).max(100).optional(),
  comment: z.string().min(1).max(250),
});

export type StatusCommentDto = z.infer<typeof StatusCommentDto>;
export type EntryRequestCreateDto = z.infer<typeof EntryRequestCreateDto>;
export type EntryRequestUpdateDto = z.infer<typeof EntryRequestUpdateDto>;
export type EntryRequestQueryDto = z.infer<typeof EntryRequestQueryDto>;
