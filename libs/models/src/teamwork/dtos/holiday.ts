import { z } from 'zod';

const ObjectIdRegex = /^[0-9a-fA-F]{24}$/;
export const HolidayCreateDto = z.object({
  _id: z.string().regex(ObjectIdRegex, 'Invalid Holiday ID').optional(),
  name: z.string().optional(),
  date: z.string(),
  country_id: z.string().regex(ObjectIdRegex, 'Invalid Country ID'),
});

export const HolidayUpdateDto = HolidayCreateDto.partial();

export const HolidayQueryDto = z.object({
  name: z.string().optional(),
  date: z.string().optional(),
  country_id: z.string().regex(ObjectIdRegex, 'Invalid Country ID').optional(),
});

export type HolidayCreateDto = z.infer<typeof HolidayCreateDto>;
export type HolidayUpdateDto = z.infer<typeof HolidayUpdateDto>;
export type HolidayQueryDto = z.infer<typeof HolidayQueryDto>;
