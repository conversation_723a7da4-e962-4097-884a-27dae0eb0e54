import { z } from 'zod';

const ObjectIdRegex = /^[0-9a-fA-F]{24}$/;
export const WorkingDayCreateDto = z.object({
  _id: z.string().regex(ObjectIdRegex, 'Invalid working day ID').optional(),
  year: z.string(),
});

export const WorkingDayUpdateDto = WorkingDayCreateDto.partial();

export const WorkingDayQueryDto = z.object({
  _id: z.string().regex(ObjectIdRegex, 'Invalid working day ID').optional(),
  year: z.string(),
});

export type WorkingDayCreateDto = z.infer<typeof WorkingDayCreateDto>;
export type WorkingDayUpdateDto = z.infer<typeof WorkingDayUpdateDto>;
export type WorkingDayQueryDto = z.infer<typeof WorkingDayQueryDto>;
