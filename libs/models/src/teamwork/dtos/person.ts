import { z } from 'zod';
import { QueryPaginationSchema } from '../../shared';
import { TeamWorkEnum } from '@shinrai-way-of-work/utils';

const ObjectIdRegex = /^[0-9a-fA-F]{24}$/;

export const PersonCreateDto = z.object({
  id_entryRequest: z.number().optional(),
  name: z.string().min(1),
  lastName: z.string().optional(),
  fullName: z.string().optional(),
  RUT: z.string().optional(),
  birthDate: z.string().optional(),
  gender: z.string(),
  nationality: z.string().optional(),
  position: z.string().optional(),
  period: z.number().optional(),
  entryDate: z.string().optional(),
  exitDate: z.string().optional(),
  SAPcode: z.string().optional(),
  MATdescription: z.string().optional(),
  budget_line: z.string().optional(),
  budget_type: z.string().optional(),
  ceco: z.string().optional(),
  purchaseOrder: z.string().optional(),
  serviceType: z.string().optional(),
  roleId: z.string().regex(ObjectIdRegex, 'Invalid role ID').optional(),
  codeAd: z.string().optional(),
  partnerId: z.string().regex(ObjectIdRegex, 'Invalid partner ID').nullish(),
  type: z.nativeEnum(TeamWorkEnum.Type).optional(),
  seniority: z.nativeEnum(TeamWorkEnum.Seniority).optional(),
  residenceCountry: z.string().optional(),
  paymentCountry: z.string().optional(),
  currency: z.string().optional(),
  workType: z.nativeEnum(TeamWorkEnum.WorkType).optional(),
  localCost: z.number().optional(),
  monthlyLocalCost: z.number().optional(),
  monthlyCostInDollar: z.number().optional(),
  rateToUsd: z.number().optional(),
  hour: z.number().optional(),
  licenses: z.array(z.string().regex(ObjectIdRegex, 'Invalid license ID')).optional(),
  email: z.string().email().optional(),
  skills: z.array(z.string().regex(ObjectIdRegex, 'Invalid skills ID')).optional(),
  conformity: z.boolean(),
  areaId: z.string().regex(ObjectIdRegex, 'Invalid area ID').optional(),
  subareaId: z.string().regex(ObjectIdRegex, 'Invalid subarea ID').optional(),
  submanagementId: z.string().regex(ObjectIdRegex, 'Invalid submanagement ID').optional(),
  areaManager: z.string().optional(),
  manager: z.string().optional(),
  submanagementManager: z.string().optional(),
  is_active: z.boolean().optional(),
});

export const PersonUpdateDto = PersonCreateDto.partial();
export const PersonBulkCreateDto = z.array(PersonCreateDto);

export const PersonQueryDto = QueryPaginationSchema.extend({
  id: z.string().regex(ObjectIdRegex, 'Invalid person ID').optional(),
  partnerId: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  type: z.nativeEnum(TeamWorkEnum.Type).optional(),
  roleId: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  residenceCountry: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  paymentCountry: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  currency: z.nativeEnum(TeamWorkEnum.Currency).optional(),
  email: z.string().email().optional(),
  seniority: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  licenses: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  areaId: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  subarea: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  submanagementId: z
    .union([z.string(), z.array(z.string())])
    .transform((val) => (Array.isArray(val) ? val : [val]))
    .optional(),
  is_active: z.enum(['true', 'false']).default('true'),
  search: z.string().min(1).optional(),
  conformity: z.enum(['true', 'false']).optional(),
});

export type PersonQueryDto = z.infer<typeof PersonQueryDto>;
export type PersonCreateDto = z.infer<typeof PersonCreateDto>;
export type PersonUpdateDto = z.infer<typeof PersonUpdateDto>;
export type PersonBulkCreateDto = z.infer<typeof PersonBulkCreateDto>;
