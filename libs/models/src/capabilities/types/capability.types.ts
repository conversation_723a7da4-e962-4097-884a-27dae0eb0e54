export interface CreateCapability {
  funcionalidad: string;
  definicion: string;
  dominio: string;
  capacidad: string;
  sub_capacidad?: string;
  foco_operativo?: string;
  cadena?: string;
  proceso?: string;
  link_diseno?: string;
  input?: string;
  output?: string;
  outcome?: string;
  responsable?: string;
  chiletxd?: boolean;
  chilesm?: boolean;
  chilemdh?: boolean;
  argentinasm?: boolean;
  argentinamdh?: boolean;
  perusm?: boolean;
  brasilsm?: boolean;
  colombiasm?: boolean;
  colombiamdh?: boolean;
}

export interface UpdateCapability {
  funcionalidad?: string;
  definicion?: string;
  dominio?: string;
  capacidad?: string;
  sub_capacidad?: string;
  foco_operativo?: string;
  cadena?: string;
  proceso?: string;
  link_diseno?: string;
  input?: string;
  output?: string;
  outcome?: string;
  responsable?: string;
  chiletxd?: boolean;
  chilesm?: boolean;
  chilemdh?: boolean;
  argentinasm?: boolean;
  argentinamdh?: boolean;
  perusm?: boolean;
  brasilsm?: boolean;
  colombiasm?: boolean;
  colombiamdh?: boolean;
}

export interface CreateBulkCapability {
  capabilities: CreateCapability[];
} 