import { z } from 'zod';

export const CreateSchema = z.object({
  funcionalidad: z.string().min(1, 'Funcionalidad es requerida'),
  definicion: z.string().min(1, 'Definición es requerida'),
  dominio: z.string().min(1, 'Dominio es requerido'),
  capacidad: z.string().min(1, 'Capacidad es requerida'),
  sub_capacidad: z.string().optional(),
  foco_operativo: z.string().optional(),
  cadena: z.string().optional(),
  proceso: z.string().optional(),
  link_diseno: z.string().optional(),
  input: z.string().optional(),
  output: z.string().optional(),
  outcome: z.string().optional(),
  responsable: z.string().optional(),
  chiletxd: z.boolean().optional(),
  chilesm: z.boolean().optional(),
  chilemdh: z.boolean().optional(),
  argentinasm: z.boolean().optional(),
  argentinamdh: z.boolean().optional(),
  perusm: z.boolean().optional(),
  brasilsm: z.boolean().optional(),
  colombiasm: z.boolean().optional(),
  colombiamdh: z.boolean().optional()
});

export const UpdateSchema = CreateSchema.partial();

export const CreateBulkSchema = z.object({
  capabilities: z.array(CreateSchema).min(1, 'Debe incluir al menos una capability')
});

export type CreateCapabilityDto = z.infer<typeof CreateSchema>;
export type UpdateCapabilityDto = z.infer<typeof UpdateSchema>;
export type CreateBulkCapabilityDto = z.infer<typeof CreateBulkSchema>; 