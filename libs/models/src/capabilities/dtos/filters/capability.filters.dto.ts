import { z } from 'zod';
import { QueryPaginationSchema } from '../../../shared';

export const QuerySchema = QueryPaginationSchema.extend({
  funcionalidad: z.string().optional(),
  definicion: z.string().optional(),
  dominio: z.string().optional(),
  capacidad: z.string().optional(),
  sub_capacidad: z.string().optional(),
  foco_operativo: z.string().optional(),
  cadena: z.string().optional(),
  proceso: z.string().optional(),
  responsable: z.string().optional(),
  chiletxd: z.boolean().optional(),
  chilesm: z.boolean().optional(),
  chilemdh: z.boolean().optional(),
  argentinasm: z.boolean().optional(),
  argentinamdh: z.boolean().optional(),
  perusm: z.boolean().optional(),
  brasilsm: z.boolean().optional(),
  colombiasm: z.boolean().optional(),
  colombiamdh: z.boolean().optional(),
  is_active: z.enum(['true', 'false']).default('true'),
  id: z
    .string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .optional(),
});

export type QueryDto = z.infer<typeof QuerySchema>; 