import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument } from 'mongoose';
import { UserData } from '../../shared';

export type CapabilityDocument = HydratedDocument<Capability>;

@Schema({ collection: 'capability', timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Capability extends Document {
  @Prop({ required: true, type: String })
  funcionalidad!: string;

  @Prop({ required: true, type: String })
  definicion!: string;

  @Prop({ required: true, type: String })
  dominio!: string;

  @Prop({ required: true, type: String })
  capacidad!: string;

  @Prop({ required: false, type: String })
  sub_capacidad?: string;

  @Prop({ required: false, type: String })
  foco_operativo?: string;

  @Prop({ required: false, type: String })
  cadena?: string;

  @Prop({ required: false, type: String })
  proceso?: string;

  @Prop({ required: false, type: String })
  link_diseno?: string;

  @Prop({ required: false, type: String })
  input?: string;

  @Prop({ required: false, type: String })
  output?: string;

  @Prop({ required: false, type: String })
  outcome?: string;

  @Prop({ required: false, type: String })
  responsable?: string;

  @Prop({ required: false, type: Boolean })
  chiletxd?: boolean;

  @Prop({ required: false, type: Boolean })
  chilesm?: boolean;

  @Prop({ required: false, type: Boolean })
  chilemdh?: boolean;

  @Prop({ required: false, type: Boolean })
  argentinasm?: boolean;

  @Prop({ required: false, type: Boolean })
  argentinamdh?: boolean;

  @Prop({ required: false, type: Boolean })
  perusm?: boolean;

  @Prop({ required: false, type: Boolean })
  brasilsm?: boolean;

  @Prop({ required: false, type: Boolean })
  colombiasm?: boolean;

  @Prop({ required: false, type: Boolean })
  colombiamdh?: boolean;

  @Prop({ default: true, type: Boolean })
  is_active!: boolean;

  @Prop({ type: UserData })
  created_by!: UserData;

  @Prop({ type: UserData })
  updated_by?: UserData;

  @Prop({ type: Date })
  created_at!: Date;

  @Prop({ type: Date })
  updated_at!: Date;
}

export const CapabilitySchema = SchemaFactory.createForClass(Capability); 