import { DynamicModule, Global } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SubManagement, SubManagementSchema } from '@shinrai-way-of-work/models';
import { SubManagementTeamWorkRepository } from './sub-management.repository';

@Global()
export class SubManagementTeamWorkRepositoryModule {
  static register(): DynamicModule {
    return {
      module: SubManagementTeamWorkRepositoryModule,
      providers: [SubManagementTeamWorkRepository],
      exports: [SubManagementTeamWorkRepository],
      imports: [
        MongooseModule.forFeature([
          {
            name: SubManagement.name,
            schema: SubManagementSchema,
            collection: SubManagement.name.toLowerCase(),
          },
        ]),
      ],
    };
  }
}