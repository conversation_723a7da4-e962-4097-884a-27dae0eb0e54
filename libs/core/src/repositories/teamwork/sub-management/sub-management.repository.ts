import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { RepositoryBase } from '../../shared';
import { SubManagement } from '@shinrai-way-of-work/models';

@Injectable()
export class SubManagementTeamWorkRepository extends RepositoryBase<SubManagement> {
  constructor(@InjectModel(SubManagement.name) SubManagementTeamWork: Model<SubManagement>) {
    super(SubManagementTeamWork);
  }

  async insertMany(docs: Partial<SubManagement>[]) {
    return this.model.insertMany(docs);
  }
}
