import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { RepositoryBase } from '../../shared';
import { Holiday } from '@shinrai-way-of-work/models';

@Injectable()
export class HolidayTeamWorkRepository extends RepositoryBase<Holiday> {
  constructor(@InjectModel(Holiday.name) Holiday: Model<Holiday>) {
    super(Holiday);
  }

  async insertMany(docs: Partial<Holiday>[]) {
    return this.model.insertMany(docs);
  }
}
