import { DynamicModule, Global } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Holiday, HolidaySchema } from '@shinrai-way-of-work/models';
import { HolidayTeamWorkRepository } from './holiday.repository';

@Global()
export class HolidayTeamWorkRepositoryModule {
  static register(): DynamicModule {
    return {
      module: HolidayTeamWorkRepositoryModule,
      providers: [HolidayTeamWorkRepository],
      exports: [HolidayTeamWorkRepository],
      imports: [
        MongooseModule.forFeature([
          {
            name: Holiday.name,
            schema: HolidaySchema,
            collection: Holiday.name.toLowerCase(),
          },
        ]),
      ],
    };
  }
}
