import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { RepositoryBase } from '../../shared';
import { EntryRequest } from '@shinrai-way-of-work/models';

@Injectable()
export class EntryRequestRepository extends RepositoryBase<EntryRequest> {
  entryRequestModel: Model<EntryRequest>;
  constructor(@InjectModel(EntryRequest.name) entryRequest: Model<EntryRequest>) {
    super(entryRequest);
    this.entryRequestModel = entryRequest;
  }
}
