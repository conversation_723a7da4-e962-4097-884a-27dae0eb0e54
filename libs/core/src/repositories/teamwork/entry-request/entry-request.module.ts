import { DynamicModule, Global } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EntryRequestRepository } from './entry-request.repository';
import { EntryRequest, EntryRequestSchema } from '@shinrai-way-of-work/models';

@Global()
export class EntryRequestRepositoryModule {
  static register(): DynamicModule {
    return {
      module: EntryRequestRepositoryModule,
      providers: [EntryRequestRepository],
      exports: [EntryRequestRepository],
      imports: [
        MongooseModule.forFeature([
          {
            name: EntryRequest.name,
            schema: EntryRequestSchema,
            collection: EntryRequest.name.toLowerCase(),
          },
        ]),
      ],
    };
  }
}
