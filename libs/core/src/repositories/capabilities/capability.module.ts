import { DynamicModule, Global } from "@nestjs/common";
import { CapabilityRepository } from "./capability.repository";
import { MongooseModule } from "@nestjs/mongoose";
import { Capability, CapabilitySchema } from "@shinrai-way-of-work/models";

@Global()
export class CapabilityRepositoryModule {

  static register(): DynamicModule {
    return {
      module: CapabilityRepositoryModule,
      providers: [CapabilityRepository],
      exports: [CapabilityRepository],
      imports: [
        MongooseModule.forFeature([{ name: Capability.name, schema: CapabilitySchema}])
      ]
    }
  }

} 