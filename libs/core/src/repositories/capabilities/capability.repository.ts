import { Injectable } from "@nestjs/common";
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { RepositoryBase } from "../shared";
import { Capability } from '@shinrai-way-of-work/models';

@Injectable()
export class CapabilityRepository extends RepositoryBase<Capability> {
  constructor(@InjectModel(Capability.name) capabilityModel: Model<Capability>) {
    super(capabilityModel);
  }
} 